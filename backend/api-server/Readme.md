## User and Password for Login

**ROLE_ADMIN**
- Username: `<EMAIL>`
- Password: `Admin1234!!`

**ROLE_USER**
- Username: `<EMAIL>`
- Password: `User1234!!`

For more information, refer to the `db/migrations/data` folder for user and role mappings.

---

## Liquibase Integration

When new entities or attributes are added, or any changes are made to existing entities, generate a Liquibase changeset for the corresponding database changes.

### Generate Changeset
```shell
mvn clean install compile liquibase:diff -Ddiff.version={new version number}
