package com.enosisbd.api.server.service.authentication;

import com.enosisbd.api.server.controller.authentication.exceptions.UserIsNotActiveException;
import com.enosisbd.api.server.controller.authentication.exceptions.UsernameAlreadyExistsException;
import com.enosisbd.api.server.dto.AuthenticationDTO.*;
import com.nimbusds.jose.JOSEException;
import jakarta.validation.Valid;

import java.util.Map;

import org.springframework.http.ResponseEntity;

public interface AuthenticationService {
    ResponseEntity<SignInResponseDTO> signIn(SignInDTO signInDTO) throws UserIsNotActiveException;
    ResponseEntity<SignUpResponseDTO> signUp(SignUpDTO signUpDTO) throws UsernameAlreadyExistsException;
    ResponseEntity<ForgotPasswordResponseDTO> forgotPassword(@Valid ForgotPasswordDTO forgotPasswordDTO);
    ResponseEntity<ResetPasswordResponseDTO> resetPassword(@Valid ResetPasswordDTO resetPasswordDTO);
    ResponseEntity<RefreshTokenResponseDTO> refreshToken(@Valid RefreshTokenDTO refreshTokenDTO) throws UserIsNotActiveException, JOSEException;
    ResponseEntity<SignInResponseDTO> processOAuth2Login(Map<String, Object> attributes, String provider,
            String accessToken, String refreshToken, Integer expiresIn);
}
