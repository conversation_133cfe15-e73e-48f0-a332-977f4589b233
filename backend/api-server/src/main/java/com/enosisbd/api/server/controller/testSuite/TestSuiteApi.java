package com.enosisbd.api.server.controller.testSuite;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.dto.TestCaseDto;
import com.enosisbd.api.server.dto.TestScriptDto;
import com.enosisbd.api.server.dto.TestSuiteDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface TestSuiteApi {
    @PostMapping
    @Operation(summary = "Create a new test suite")
    @ApiResponse(responseCode = "201", description = "Test suite created successfully")
    ResponseEntity<RestResponse<TestSuiteDto>> add(@Valid @RequestBody TestSuiteDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get test suite by ID")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    RestResponse<TestSuiteDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    RestResponse<TestSuiteDto> update(
            @PathVariable Long id,
            @Valid @RequestBody TestSuiteDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete test suite")
    @ApiResponse(responseCode = "204", description = "Test suite deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    ResponseEntity<Void> delete(@PathVariable Long id);

    @GetMapping("/{id}/test-cases")
    @Operation(summary = "Get test cases for a test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    RestResponse<List<TestCaseDto>> findTestCases(@PathVariable Long id);

    @GetMapping("/{id}/crawled-pages")
    @Operation(summary = "Get crawled pages for a test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    RestResponse<List<CrawledPageDto>> findCrawledPages(@PathVariable Long id);

    @GetMapping("/{id}/test-script")
    @Operation(summary = "Get test script for a test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    ResponseEntity<RestResponse<TestScriptDto>> findTestScript(@PathVariable Long id);
}
