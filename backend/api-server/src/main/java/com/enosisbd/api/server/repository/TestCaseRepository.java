package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.TestCase;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface TestCaseRepository extends BaseRepository<TestCase, Long> {
    @Query("from TestCase tc left join fetch tc.testSuite")
    List<TestCase> findAllJoined();

    @Query("from TestCase tc left join fetch tc.testSuite where tc.id = :id")
    Optional<TestCase> findByIdJoined(Long id);

    @Query("from TestCase tc left join fetch tc.testSuite ts where ts.id = :testSuiteId")
    List<TestCase> findByTestSuiteIdJoined(Long testSuiteId);

    @Query("select count(tc) from TestCase tc where tc.testSuite.id = :testSuiteId")
    Integer countByTestSuiteId(Long testSuiteId);


}