package com.enosisbd.api.server.controller.googlesheets;

import com.enosisbd.api.server.config.GoogleSheetsProperties;
import com.enosisbd.api.server.dto.GoogleSheetImportRequestDto;
import com.enosisbd.api.server.dto.GoogleSheetImportResponseDto;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsImportService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.security.GeneralSecurityException;

@RestController
@RequestMapping("/api/google-sheets")
@RequiredArgsConstructor
@Log4j2
@Tag(name = "Google Sheets Controller", description = "APIs for Google Sheets integration")
public class GoogleSheetsController implements GoogleSheetsApi {

    private final GoogleSheetsImportService googleSheetsImportService;
    private final GoogleSheetsService googleSheetsService;
    private final AuthorizationService authorizationService;
    private final UserService userService;
    private final GoogleSheetsProperties googleSheetsProperties;

    @Override
    @PostMapping("/import")
    @Operation(summary = "Import data from a Google Sheet")
    @ApiResponse(responseCode = "201", description = "Project created successfully from Google Sheet")
    public ResponseEntity<RestResponse<GoogleSheetImportResponseDto>> importFromGoogleSheet(
            @Valid @RequestBody GoogleSheetImportRequestDto request)
            throws IOException, GeneralSecurityException {

        log.info("Importing data from Google Sheet: {}", request.getSheetUrl());

        // Get the current user's email
        String currentUsername = authorizationService.getCurrentUsername();
        String userEmail = currentUsername; // Default to current username

        // If the user is authenticated with Google, use their Google email for access control
        User user = userService.findByEmail(currentUsername).orElse(null);
        if (user != null && "GOOGLE".equalsIgnoreCase(user.getProvider())) {
            userEmail = user.getEmail();
            log.info("Using Google OAuth user email for access control: {}", userEmail);
        } else {
            log.info("Using current user email for Google Sheets access: {}", userEmail);
        }

        try {
            // Extract sheet ID from URL
            String sheetId = googleSheetsService.extractSheetId(request.getSheetUrl());

            // Check if the user has access to the sheet before proceeding (if checkAccess is enabled)
            if (googleSheetsProperties.isCheckAccess() && !googleSheetsService.hasAccessToSheet(sheetId, userEmail)) {
                throw new BadRequestRestException("You don't have access to this Google Sheet. Please make sure the sheet is shared with your Google account.");
            }

            log.info("User has access to the Google Sheet. Proceeding with import.");
        } catch (Exception e) {
            log.error("Error checking access to Google Sheet: {}", e.getMessage());
            throw new BadRequestRestException("Error checking access to Google Sheet: " + e.getMessage());
        }

        // Set the user email in the request
        request.setGoogleUserEmail(userEmail);

        GoogleSheetImportResponseDto response = googleSheetsImportService.importFromGoogleSheet(request, userEmail);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(response));
    }
}
