package com.enosisbd.api.server.service.version.impl;

import com.enosisbd.api.server.dto.VersionDto;
import com.enosisbd.api.server.entity.Version;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.repository.TestSuiteRepository;
import com.enosisbd.api.server.repository.VersionRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.version.VersionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * The Version service.
 */
@Service
@RequiredArgsConstructor
public class VersionServiceImpl implements VersionService {

    private final VersionRepository repository;
    private final ProjectRepository projectRepository;
    private final TestSuiteRepository testSuiteRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;

    /**
     * Exists by id boolean.
     *
     * @param id the id
     * @return the boolean
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        Optional<Version> version = repository.findById(id);
        if (version.isEmpty()) {
            return false;
        }

        return entitySharingService.hasAccess(version.get(), EntityType.VERSION);
    }

    /**
     * Gets by id.
     *
     * @param id the id
     * @return the by id
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<VersionDto> getById(Long id) {
        Optional<Version> version = repository.findByIdJoined(id);
        if (version.isEmpty()) {
            return Optional.empty();
        }

        // Access check is handled by the @RequiresEntityAccess annotation
        return version.map(this::convertToDto);
    }

    /**
     * Add version entity.
     *
     * @param dto the dto
     * @return the version dto
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public VersionDto add(VersionDto dto) {
        String currentUsername = authorizationService.getCurrentUsername();

        if (dto.getProjectId() != null) {
            // Check if user has access to the project
            var project = projectRepository
                    .findById(dto.getProjectId())
                    .orElseThrow(() -> BadRequestRestException.with("Project not found with ID: " + dto.getProjectId()));

            if (!entitySharingService.hasAccess(project, EntityType.PROJECT)) {
                throw new BadRequestRestException("You don't have access to this project");
            }
        }

        Version entity = convertToEntity(new Version(), dto);
        entity.setCreatedBy(currentUsername);

        if (dto.getProjectId() != null) {
            var project = projectRepository
                    .findById(dto.getProjectId())
                    .orElseThrow(() -> BadRequestRestException.with("projectId not found"));
            entity.setProject(project);
        }

        repository.save(entity);
        return convertToDto(entity);
    }

    /**
     * Update version optional.
     *
     * @param dto the dto
     * @return the optional
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<VersionDto> update(VersionDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        Version existing = maybe.get();
        // Access check is handled by the @RequiresEntityAccess annotation

        Version entity = convertToEntity(existing, dto);

        boolean isValidId = dto.getProjectId() != null
                && !Objects.equals(dto.getProjectId(), entity.getProjectId());

        if (isValidId) {
            // Check if user has access to the project
            var project = projectRepository
                    .findById(dto.getProjectId())
                    .orElseThrow(() -> BadRequestRestException.with("Project not found with ID: " + dto.getProjectId()));
            entity.setProject(project);
        }

        repository.save(entity);
        return Optional.of(convertToDto(entity));
    }

    /**
     * Delete Version optional.
     *
     * @param id the id
     * @return the optional
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<Version> version = repository.findById(id);
        if (version.isEmpty()) {
            return Optional.empty();
        }

        // Access check is handled by the @RequiresEntityAccess annotation

        // Delete the version
        repository.delete(version.get());
        return Optional.of(true);
    }



    /**
     * Find Versions by project id list.
     *
     * @param projectId the project id
     * @return the list
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<VersionDto> findByProjectId(Long projectId) {
        // Get current user ID and username
        Long userId = authorizationService.getCurrentUserId();
        String currentUsername = authorizationService.getCurrentUsername();
        List<Version> allVersions = repository.findByProjectIdJoined(projectId);

        // If user is admin, return all versions for the project
        if (authorizationService.isCurrentUserAdmin()) {
            return allVersions.stream()
                    .map(this::convertToDto)
                    .toList();
        }

        // Check if the user is the creator of the project
        boolean isProjectCreator = projectRepository.findById(projectId)
                .map(project -> project.getCreatedBy() != null && project.getCreatedBy().equals(currentUsername))
                .orElse(false);

        // If user is the project creator, return all versions
        if (isProjectCreator) {
            return allVersions.stream()
                    .map(this::convertToDto)
                    .toList();
        }

        // Check if user has direct non-inherited access to the project (shared directly with them, not through version)
        boolean hasNonInheritedDirectProjectAccess = entitySharingService.hasNonInheritedDirectAccess(EntityType.PROJECT, projectId, userId);

        // If user has direct non-inherited access to the project, return all versions
        if (hasNonInheritedDirectProjectAccess) {
            return allVersions.stream()
                    .map(this::convertToDto)
                    .toList();
        }

        // User doesn't have direct non-inherited access to the project, so they must have access through version sharing
        // Get all version IDs that the user has direct non-inherited access to
        List<Long> directAccessVersionIds = entitySharingService.getNonInheritedDirectAccessEntityIds(EntityType.VERSION, userId);

        // Filter versions to only include those the user has direct non-inherited access to
        List<Version> accessibleVersions = allVersions.stream()
                .filter(version ->
                    // Include if user is the creator of the version
                    (version.getCreatedBy() != null && version.getCreatedBy().equals(currentUsername)) ||
                    // Include if user has direct non-inherited access to the version
                    directAccessVersionIds.contains(version.getId())
                )
                .toList();

        // Convert to DTOs
        return accessibleVersions.stream()
                .map(this::convertToDto)
                .toList();
    }

    private VersionDto convertToDto(Version entity) {
        VersionDto dto = new VersionDto();
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setName(entity.getName());
        dto.setProjectId(entity.getProjectId());
        dto.setTestCaseGenerationMethod(entity.getTestCaseGenerationMethod());
        dto.setPlatform(entity.getPlatform());
        dto.setLanguage(entity.getLanguage());
        dto.setCreatedBy(entity.getCreatedBy());
        // Set the number of test suites
        if (entity.getId() != null) {
            Integer testSuiteCount = testSuiteRepository.countByVersionId(entity.getId());
            dto.setNumberOfTestSuites(testSuiteCount != null ? testSuiteCount : 0);
        } else {
            dto.setNumberOfTestSuites(0);
        }

        return dto;
    }

    private Version convertToEntity(Version entity, VersionDto dto) {
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setTestCaseGenerationMethod(dto.getTestCaseGenerationMethod());
        entity.setPlatform(dto.getPlatform());
        entity.setLanguage(dto.getLanguage());
        return entity;
    }
}
