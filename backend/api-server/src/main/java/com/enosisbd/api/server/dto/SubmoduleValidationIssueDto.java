package com.enosisbd.api.server.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for representing a submodule validation issue with correction details
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmoduleValidationIssueDto {
    private String originalName;
    private String correctedName;
    private String sheetName;
    private String columnLetter;
    private Integer rowNumber; // 1-based row number for user display
    private String cellReference; // e.g., "A5", "B12"
    private String issueType; // e.g., "NAME_TOO_SHORT", "EMPTY_NAME"
    private String description; // Human-readable description of the issue
}
