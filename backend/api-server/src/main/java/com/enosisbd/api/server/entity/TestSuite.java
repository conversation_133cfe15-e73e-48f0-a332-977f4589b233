package com.enosisbd.api.server.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(indexes = {
    @Index(name = "idx_test_suite_updated_at", columnList = "updatedAt"),
    @Index(name = "idx_test_suite_version", columnList = "version_id")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TestSuite extends BaseEntity {
    @NotNull(message = "Title cannot be null")
    @NotBlank(message = "Title cannot be empty")
    @Size(min = 3, max = 255, message = "Title must be between 3 and 255 characters")
    @Column(nullable = false)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String requirement;

    @OneToMany(mappedBy = "testSuite", cascade = CascadeType.ALL)
    @JsonManagedReference(value = "testSuite-testCases")
    List<TestCase> testCases = new ArrayList<>();

    @OneToMany(mappedBy = "testSuite", cascade = CascadeType.ALL)
    private List<CrawledPage> crawledPages;

    @OneToOne(mappedBy = "testSuite", cascade = CascadeType.ALL)
    @JsonManagedReference(value = "testSuite-testScript")
    private TestScript testScript;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "version_id")
    @JsonBackReference
    private Version versionEntity;

    public Long getVersionId() {
        return Optional.ofNullable(versionEntity)
                .map(Version::getId)
                .orElse(null);
    }

    /**
     * Adds a test case to this test suite.
     * @param testCase The test case to add
     */
    public void addTestCase(TestCase testCase) {
        testCases.add(testCase);
        testCase.setTestSuite(this);
    }

    /**
     * Removes a test case from this test suite.
     * @param testCase The test case to remove
     */
    public void removeTestCase(TestCase testCase) {
        testCases.remove(testCase);
        testCase.setTestSuite(null);
    }

    /**
     * Sets the version entity and updates the relationship.
     * @param versionEntity The version entity to associate with
     */
    public void setVersionEntity(Version versionEntity) {
        this.versionEntity = versionEntity;
    }
}
