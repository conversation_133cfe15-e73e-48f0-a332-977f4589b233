package com.enosisbd.api.server.controller.testSuite;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.dto.TestCaseDto;
import com.enosisbd.api.server.dto.TestScriptDto;
import com.enosisbd.api.server.dto.TestSuiteDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.crawledPage.impl.CrawledPageServiceImpl;
import com.enosisbd.api.server.service.testCase.TestCaseService;
import com.enosisbd.api.server.service.testScript.TestScriptService;
import com.enosisbd.api.server.service.testSuite.TestSuiteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/test-suites")
@RequiredArgsConstructor
@Tag(name = "Test Suite Controller", description = "APIs for test suite management")
public class TestSuiteController implements TestSuiteApi {
    private final TestSuiteService service;
    private final TestCaseService testCaseService;
    private final TestScriptService testScriptService;
    private final CrawledPageServiceImpl crawledPageService;

    @PostMapping
    @Operation(summary = "Create a new test suite")
    @ApiResponse(responseCode = "201", description = "Test suite created successfully")
    @Override
    public ResponseEntity<RestResponse<TestSuiteDto>> add(@Valid @RequestBody TestSuiteDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(service.add(dto)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get test suite by ID")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    @Override
    public RestResponse<TestSuiteDto> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Test suite not found with ID: " + id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    @Override
    public RestResponse<TestSuiteDto> update(
            @PathVariable Long id,
            @Valid @RequestBody TestSuiteDto dto) {
        dto.setId(id);
        return service.update(dto)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Test suite not found with ID: " + id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete test suite")
    @ApiResponse(responseCode = "204", description = "Test suite deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        return service.delete(id)
                .map(ignored -> ResponseEntity.noContent().<Void>build())
                .orElseThrow(() -> NotFoundRestException.with("Test suite not found with ID: " + id));
    }

    @GetMapping("/{id}/test-cases")
    @Operation(summary = "Get test cases for a test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    @Override
    public RestResponse<List<TestCaseDto>> findTestCases(@PathVariable Long id) {
        if (!service.existsById(id)) {
            throw NotFoundRestException.with("Test suite not found with ID: " + id);
        }
        return RestResponse.of(testCaseService.findByTestSuiteId(id));
    }

    @GetMapping("/{id}/crawled-pages")
    @Operation(summary = "Get crawled pages for a test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    @Override
    public RestResponse<List<CrawledPageDto>> findCrawledPages(@PathVariable Long id) {
        if (!service.existsById(id)) {
            throw NotFoundRestException.with("Test suite not found with ID: " + id);
        }
        return RestResponse.of(crawledPageService.findByTestSuiteId(id));
    }

    @GetMapping("/{id}/test-script")
    @Operation(summary = "Get test script for a test suite")
    @RequiresEntityAccess(entityType = EntityType.TEST_SUITE)
    @Override
    public ResponseEntity<RestResponse<TestScriptDto>> findTestScript(@PathVariable Long id) {
        if (!service.existsById(id)) {
            throw NotFoundRestException.with("Test suite not found with ID: " + id);
        }

        Optional<TestScriptDto> testScript = testScriptService.findByTestSuiteId(id);
        // Return empty data with 200 status instead of error message
        return ResponseEntity.ok(RestResponse.of(testScript.orElse(null)));
    }
}
