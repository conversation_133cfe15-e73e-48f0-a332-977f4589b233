package com.enosisbd.api.server.service.entitySharing.impl;

import com.enosisbd.api.server.dto.UserDTO;
import com.enosisbd.api.server.entity.BaseEntity;
import com.enosisbd.api.server.entity.EntityAccess;
import com.enosisbd.api.server.entity.SharableEntityType;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.EntityAccessRepository;
import com.enosisbd.api.server.repository.SharableEntityTypeRepository;
import com.enosisbd.api.server.repository.UserRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entityHierarchy.EntityHierarchyService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.user.UserService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Implementation of the EntitySharingService.
 * This service consolidates all entity sharing functionality.
 */
@Service
@Transactional
@RequiredArgsConstructor
public class EntitySharingServiceImpl implements EntitySharingService {

    private final EntityAccessRepository entityAccessRepository;
    private final SharableEntityTypeRepository sharableEntityTypeRepository;
    private final UserRepository userRepository;
    private final EntityHierarchyService entityHierarchyService;
    private final AuthorizationService authorizationService;
    private final UserService userService;

    /**
     * Share a project with a user
     * This grants access to the project and all its children (versions, test suites, etc.)
     */
    @Override
    public void shareProject(Long projectId, Long sharedByUserId, Long sharedWithUserId) {
        // Validate project exists
        if (!entityHierarchyService.entityExists(EntityType.PROJECT, projectId)) {
            throw new BadRequestRestException("Project not found with ID: " + projectId);
        }

        // Share the project
        shareEntity(EntityType.PROJECT.getName(), projectId, sharedByUserId, sharedWithUserId);
    }

    /**
     * Share a module with a user
     * This grants access to the parent project, this specific module, and all children of this module
     */
    @Override
    public void shareModule(Long moduleId, Long sharedByUserId, Long sharedWithUserId) {
        // Validate module exists
        if (!entityHierarchyService.entityExists(EntityType.MODULE, moduleId)) {
            throw new BadRequestRestException("Module not found with ID: " + moduleId);
        }

        // Share the module directly
        shareEntity(EntityType.MODULE.getName(), moduleId, sharedByUserId, sharedWithUserId);

        // Share the parent project as inherited access
        Map.Entry<EntityType, Long> parent = entityHierarchyService.getParentEntity(EntityType.MODULE, moduleId);
        if (parent != null) {
            // Share the parent project with inherited access
            shareEntityInherited(
                parent.getKey().getName(),
                parent.getValue(),
                sharedByUserId,
                sharedWithUserId
            );
        }
    }

    /**
     * Share a submodule with a user
     * This grants access to the parent project, parent module, this specific submodule, and all children of this submodule
     */
    @Override
    public void shareSubModule(Long subModuleId, Long sharedByUserId, Long sharedWithUserId) {
        // Validate submodule exists
        if (!entityHierarchyService.entityExists(EntityType.SUBMODULE, subModuleId)) {
            throw new BadRequestRestException("SubModule not found with ID: " + subModuleId);
        }

        // Share the submodule directly
        shareEntity(EntityType.SUBMODULE.getName(), subModuleId, sharedByUserId, sharedWithUserId);

        // Share the parent module as inherited access
        Map.Entry<EntityType, Long> parent = entityHierarchyService.getParentEntity(EntityType.SUBMODULE, subModuleId);
        if (parent != null) {
            // Share the parent module with inherited access
            shareEntityInherited(
                parent.getKey().getName(),
                parent.getValue(),
                sharedByUserId,
                sharedWithUserId
            );

            // Share the grandparent project as inherited access
            Map.Entry<EntityType, Long> grandparent = entityHierarchyService.getParentEntity(parent.getKey(), parent.getValue());
            if (grandparent != null) {
                // Share the grandparent project with inherited access
                shareEntityInherited(
                    grandparent.getKey().getName(),
                    grandparent.getValue(),
                    sharedByUserId,
                    sharedWithUserId
                );
            }
        }
    }

    /**
     * Remove a user's access to a project
     * This removes access to the project and all its children
     */
    @Override
    public void removeProjectAccess(Long projectId, Long sharedWithUserId) {
        Long currentUserId = authorizationService.getCurrentUserId();
        boolean isAdmin = authorizationService.isCurrentUserAdmin();
        boolean isCreator = isCreator(currentUserId, EntityType.PROJECT, projectId);

        // If user is not admin or creator, they cannot remove access
        if (!isAdmin && !isCreator) {
            throw new BadRequestRestException("You don't have permission to remove access to this project");
        }

        // Validate project exists
        if (!entityHierarchyService.entityExists(EntityType.PROJECT, projectId)) {
            throw new BadRequestRestException("Project not found with ID: " + projectId);
        }

        // Remove project access
        removeAccess(EntityType.PROJECT.getName(), projectId, sharedWithUserId);

        // Remove access to all children recursively
        removeChildrenAccess(EntityType.PROJECT, projectId, sharedWithUserId);
    }

    /**
     * Remove a user's access to a module
     * This removes access to the module and all its children, but not to the parent project
     */
    @Override
    public void removeModuleAccess(Long moduleId, Long sharedWithUserId) {
        Long currentUserId = authorizationService.getCurrentUserId();
        boolean isAdmin = authorizationService.isCurrentUserAdmin();
        boolean isCreator = isCreator(currentUserId, EntityType.MODULE, moduleId);

        // If user is not admin or creator, they cannot remove access
        if (!isAdmin && !isCreator) {
            throw new BadRequestRestException("You don't have permission to remove access to this module");
        }

        // Validate module exists
        if (!entityHierarchyService.entityExists(EntityType.MODULE, moduleId)) {
            throw new BadRequestRestException("Module not found with ID: " + moduleId);
        }

        // Remove module access
        removeAccess(EntityType.MODULE.getName(), moduleId, sharedWithUserId);

        // Remove access to all children recursively
        removeChildrenAccess(EntityType.MODULE, moduleId, sharedWithUserId);
    }

    /**
     * Remove a user's access to a submodule
     * This removes access to the submodule and all its children, but not to the parent module or project
     */
    @Override
    public void removeSubModuleAccess(Long subModuleId, Long sharedWithUserId) {
        Long currentUserId = authorizationService.getCurrentUserId();
        boolean isAdmin = authorizationService.isCurrentUserAdmin();
        boolean isCreator = isCreator(currentUserId, EntityType.SUBMODULE, subModuleId);

        // If user is not admin or creator, they cannot remove access
        if (!isAdmin && !isCreator) {
            throw new BadRequestRestException("You don't have permission to remove access to this submodule");
        }

        // Validate submodule exists
        if (!entityHierarchyService.entityExists(EntityType.SUBMODULE, subModuleId)) {
            throw new BadRequestRestException("SubModule not found with ID: " + subModuleId);
        }

        // Remove submodule access
        removeAccess(EntityType.SUBMODULE.getName(), subModuleId, sharedWithUserId);

        // Remove access to all children recursively
        removeChildrenAccess(EntityType.SUBMODULE, subModuleId, sharedWithUserId);
    }

    /**
     * Recursively remove access to all children of an entity
     */
    private void removeChildrenAccess(EntityType entityType, Long entityId, Long sharedWithUserId) {
        List<Map.Entry<EntityType, Long>> children = entityHierarchyService.getChildEntities(entityType, entityId);

        for (Map.Entry<EntityType, Long> child : children) {
            // Remove access to this child
            removeAccess(child.getKey().getName(), child.getValue(), sharedWithUserId);

            // Recursively remove access to its children
            removeChildrenAccess(child.getKey(), child.getValue(), sharedWithUserId);
        }
    }

    /**
     * Check if a user has access to a specific entity
     * This checks direct access, creator access, admin access, and inherited access
     */
    @Override
    public boolean hasAccess(Long userId, EntityType entityType, Long entityId) {
        // If user is admin, they have access to everything
        if (authorizationService.isAdmin(userId)) {
            return true;
        }

        // Check if user is the creator of the entity
        if (isCreator(userId, entityType, entityId)) {
            return true;
        }

        // Check direct access
        if (hasDirectAccess(entityType, entityId, userId)) {
            return true;
        }

        // Check inherited access by recursively checking parent entities
        return hasInheritedAccess(userId, entityType, entityId);
    }

    /**
     * Check if the current user has access to a specific entity
     */
    @Override
    public boolean hasAccess(BaseEntity entity, EntityType entityType) {
        if (entity == null) {
            return false;
        }

        // Admin users have access to everything
        if (authorizationService.isCurrentUserAdmin()) {
            return true;
        }

        // Creators have access to their own entities
        String currentUsername = authorizationService.getCurrentUsername();
        if (entity.getCreatedBy() != null && entity.getCreatedBy().equals(currentUsername)) {
            return true;
        }

        // Check if the entity is shared with the user
        Long userId = authorizationService.getCurrentUserId();
        return hasAccess(userId, entityType, entity.getId());
    }

    /**
     * Check if a user has inherited access to an entity through its parent entities
     */
    private boolean hasInheritedAccess(Long userId, EntityType entityType, Long entityId) {
        // Get parent entity
        Map.Entry<EntityType, Long> parent = entityHierarchyService.getParentEntity(entityType, entityId);

        // If there's no parent, there's no inherited access
        if (parent == null) {
            return false;
        }

        // Check access to the parent entity
        return hasAccess(userId, parent.getKey(), parent.getValue());
    }

    /**
     * Get all users who have direct non-inherited access to a specific entity
     * This only includes users with direct non-inherited access, not users with inherited access
     */
    @Override
    public List<User> getUsersWithDirectAccess(EntityType entityType, Long entityId) {
        // Get entity type
        SharableEntityType sharableEntityType = getSharableEntityType(entityType.getName());

        // If entity type not found, return empty list
        if (sharableEntityType == null) {
            return List.of();
        }

        return entityAccessRepository.findUsersWithDirectAccessByEntityTypeIdAndEntityId(
                sharableEntityType.getId(), entityId);
    }

    /**
     * Check if a user has direct access to an entity
     */
    @Override
    public boolean hasDirectAccess(EntityType entityType, Long entityId, Long userId) {
        // Get entity type
        SharableEntityType sharableEntityType = getSharableEntityType(entityType.getName());

        // If entity type not found, return false
        if (sharableEntityType == null) {
            return false;
        }

        return entityAccessRepository.existsBySharedWithUserIdAndEntityTypeIdAndEntityId(
                userId, sharableEntityType.getId(), entityId);
    }

    /**
     * Check if a user has direct non-inherited access to an entity
     * This checks if the user is the creator or has direct non-inherited access
     */
    @Override
    public boolean hasNonInheritedDirectAccess(EntityType entityType, Long entityId, Long userId) {
        if (authorizationService.isAdmin(userId)) {
            return true;
        }

        // Check if user is the creator of the entity
        if (isCreator(userId, entityType, entityId)) {
            return true;
        }

        // Get sharable entity type
        SharableEntityType sharableEntityType = getSharableEntityType(entityType.getName());

        // If entity type not found, return false
        if (sharableEntityType == null) {
            return false;
        }

        return entityAccessRepository.existsBySharedWithUserIdAndEntityTypeIdAndEntityIdAndNotInherited(
                userId, sharableEntityType.getId(), entityId);
    }

    /**
     * Get all entity IDs of a specific type that a user has direct non-inherited access to
     * This includes entities the user created and entities shared directly with the user (not through inheritance)
     */
    @Override
    public List<Long> getNonInheritedDirectAccessEntityIds(EntityType entityType, Long userId) {
        // Get entity type
        SharableEntityType sharableEntityType = getSharableEntityType(entityType.getName());

        // If entity type not found, return empty list
        if (sharableEntityType == null) {
            return List.of();
        }

        return entityAccessRepository.findDirectAccessEntityIdsByUserIdAndEntityTypeId(userId, sharableEntityType.getId());
    }

    /**
     * Filter a list of entities based on user access
     * @param entities The list of entities to filter
     * @param entityType The type of entities
     * @return A filtered list containing only entities the user has access to
     */
    @Override
    public <T extends BaseEntity> List<T> filterAccessibleEntities(List<T> entities, EntityType entityType) {
        if (authorizationService.isCurrentUserAdmin()) {
            return entities; // Admins can see all entities
        }

        String currentUsername = authorizationService.getCurrentUsername();
        Long userId = authorizationService.getCurrentUserId();

        // Filter entities created by the user
        List<T> accessibleEntities = entities.stream()
                .filter(entity -> entity.getCreatedBy() != null &&
                                 entity.getCreatedBy().equals(currentUsername))
                .collect(Collectors.toList());

        // Add entities the user has access to (directly or indirectly through parent entities)
        List<T> sharedEntities = entities.stream()
                .filter(entity -> !accessibleEntities.contains(entity) &&
                                 hasAccess(userId, entityType, entity.getId()))
                .collect(Collectors.toList());

        // Combine both lists and remove duplicates
        return Stream.concat(accessibleEntities.stream(), sharedEntities.stream())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * Get all entities of a specific type that the current user has access to
     * @param allEntities All entities of the specified type
     * @param entityType The type of entities
     * @return A list of entities the user has access to
     */
    @Override
    public <T extends BaseEntity> List<T> getAccessibleEntities(List<T> allEntities, EntityType entityType) {
        return filterAccessibleEntities(allEntities, entityType);
    }

    /**
     * Check if a user is the creator of an entity
     */
    @Override
    public boolean isCreator(Long userId, EntityType entityType, Long entityId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BadRequestRestException("User not found with ID: " + userId));
        String username = user.getUsername();

        String creatorUsername = entityHierarchyService.getEntityCreator(entityType, entityId);
        return creatorUsername != null && creatorUsername.equals(username);
    }

    /**
     * Get the creator of an entity
     */
    @Override
    public String getEntityCreator(EntityType entityType, Long entityId) {
        return entityHierarchyService.getEntityCreator(entityType, entityId);
    }

    /**
     * Check if an entity type is sharable
     */
    @Override
    public boolean isEntitySharable(EntityType entityTypeName) {
        return sharableEntityTypeRepository.findByName(entityTypeName.getName())
                .map(SharableEntityType::isActive)
                .orElse(false);
    }

    /**
     * Validate if an entity type is valid and sharable
     * @param entityType The entity type to validate
     * @throws BadRequestRestException if the entity type is invalid or not sharable
     */
    @Override
    public void validateEntityType(EntityType entityType) {
        // Check if it's sharable
        if (!isEntitySharable(entityType)) {
            throw new BadRequestRestException("Non-sharable entity type: " + entityType.getName());
        }
    }

    /**
     * Share an entity with a user based on entity type
     * @param entityType The type of entity to share
     * @param entityId The ID of the entity to share
     * @param currentUserId The ID of the user sharing the entity
     * @param sharedWithUserId The ID of the user to share with
     * @return true if the entity was shared successfully
     */
    @Override
    public boolean shareEntityByType(EntityType entityType, Long entityId, Long currentUserId, Long sharedWithUserId) {
        // Validate entity type
        validateEntityType(entityType);

        switch (entityType) {
            case PROJECT:
                shareProject(entityId, currentUserId, sharedWithUserId);
                break;
            case MODULE:
                shareModule(entityId, currentUserId, sharedWithUserId);
                break;
            case SUBMODULE:
                shareSubModule(entityId, currentUserId, sharedWithUserId);
                break;
            default:
                throw new BadRequestRestException("Unsupported entity type: " + entityType.getName());
        }

        return true;
    }

    /**
     * Remove a user's access to an entity based on entity type
     * @param entityType The type of entity to remove access from
     * @param entityId The ID of the entity to remove access from
     * @param userId The ID of the user to remove access for
     * @return true if access was removed successfully
     */
    @Override
    public boolean removeAccessByType(EntityType entityType, Long entityId, Long userId) {
        // Validate entity type
        validateEntityType(entityType);

        switch (entityType) {
            case PROJECT:
                removeProjectAccess(entityId, userId);
                break;
            case MODULE:
                removeModuleAccess(entityId, userId);
                break;
            case SUBMODULE:
                removeSubModuleAccess(entityId, userId);
                break;
            default:
                throw new BadRequestRestException("Unsupported entity type: " + entityType.getName());
        }

        return true;
    }

    /**
     * Get users who have direct access to an entity and convert to DTOs
     * @param entityType The type of entity
     * @param entityId The ID of the entity
     * @return List of UserDTOs representing users with direct access
     */
    @Override
    public List<UserDTO> getUsersWithDirectAccessAsDto(EntityType entityType, Long entityId) {
        // Validate entity type
        validateEntityType(entityType);

        // Get users with direct non-inherited access
        List<User> users = getUsersWithDirectAccess(entityType, entityId);

        // Convert to DTOs
        return users.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Search for users who can be shared with
     * @param query The search query
     * @param currentUserId The ID of the current user
     * @param entityType The type of entity (optional)
     * @param entityId The ID of the entity (optional)
     * @return List of UserDTOs representing users who can be shared with
     */
    @Override
    public List<UserDTO> searchUsersForSharing(String query, Long currentUserId, EntityType entityType, Long entityId) {
        try {
            // Search for users
            List<User> users = userService.searchUsersForSharing(query, currentUserId,
                    entityType != null ? entityType.getName() : null, entityId);

            // If entityType and entityId are provided, exclude the creator of the entity
            if (entityType != null && entityId != null) {
                // Get the entity creator
                String creatorEmail = getEntityCreator(entityType, entityId);

                // Filter out the creator
                if (creatorEmail != null) {
                    final String finalCreatorEmail = creatorEmail;
                    users = users.stream()
                            .filter(user -> !finalCreatorEmail.equals(user.getEmail()))
                            .collect(Collectors.toList());
                }
            }

            // Convert to DTOs
            return users.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // Log the error
            System.err.println("Error searching users: " + e.getMessage());
            e.printStackTrace();

            // Return an empty list instead of an error
            return new ArrayList<>();
        }
    }

    /**
     * Convert a User entity to a UserDTO
     * @param user The User entity to convert
     * @return The converted UserDTO
     */
    private UserDTO convertToDto(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setFullName(user.getFullName());
        dto.setEmail(user.getEmail());
        dto.setApproved(user.getApproved());
        return dto;
    }

    /**
     * Share an entity with a user
     */
    private void shareEntity(String entityTypeName, Long entityId, Long sharedByUserId, Long sharedWithUserId) {
        // Get entity type
        SharableEntityType entityType = getSharableEntityType(entityTypeName);

        // Check if entity type is active
        if (entityType == null || !entityType.isActive()) {
            throw new BadRequestRestException("Entity type is not sharable: " + entityTypeName);
        }

        // Get the existing access record
        EntityAccess existingAccess = entityAccessRepository.findBySharedWithUserIdAndEntityTypeIdAndEntityId(
                sharedWithUserId, entityType.getId(), entityId);
        if (existingAccess != null) {
            // Update inherited access to direct access if needed
            updateInheritedAccessToDirectAccess(existingAccess, sharedByUserId);
            return;
        }

        // Get users
        User sharedByUser = userRepository.findById(sharedByUserId)
                .orElseThrow(() -> new BadRequestRestException("Invalid shared by user ID: " + sharedByUserId));
        User sharedWithUser = userRepository.findById(sharedWithUserId)
                .orElseThrow(() -> new BadRequestRestException("Invalid shared with user ID: " + sharedWithUserId));

        // Create access record
        EntityAccess access = new EntityAccess();
        access.setEntityType(entityType);
        access.setEntityId(entityId);
        access.setSharedByUser(sharedByUser);
        access.setSharedWithUser(sharedWithUser);
        access.setIsInherited(false); // Direct access

        entityAccessRepository.save(access);
    }

    /**
     * Share an entity with a user through inheritance
     * This is used when sharing a child entity also grants access to parent entities
     */
    private void shareEntityInherited(
            String entityTypeName,
            Long entityId,
            Long sharedByUserId,
            Long sharedWithUserId) {

        // Get entity type
        SharableEntityType entityType = getSharableEntityType(entityTypeName);

        // Check if entity type is active
        if (entityType == null || !entityType.isActive()) {
            throw new BadRequestRestException("Entity type is not sharable: " + entityTypeName);
        }

        // Check if access already exists
        if (entityAccessRepository.existsBySharedWithUserIdAndEntityTypeIdAndEntityId(
                sharedWithUserId, entityType.getId(), entityId)) {
            return;
        }

        // Get users
        User sharedByUser = userRepository.findById(sharedByUserId)
                .orElseThrow(() -> new BadRequestRestException("Invalid shared by user ID: " + sharedByUserId));
        User sharedWithUser = userRepository.findById(sharedWithUserId)
                .orElseThrow(() -> new BadRequestRestException("Invalid shared with user ID: " + sharedWithUserId));

        // Create access record
        EntityAccess access = new EntityAccess();
        access.setEntityType(entityType);
        access.setEntityId(entityId);
        access.setSharedByUser(sharedByUser);
        access.setSharedWithUser(sharedWithUser);
        access.setIsInherited(true); // Inherited access

        entityAccessRepository.save(access);
    }

    /**
     * Remove a user's access to an entity
     */
    private void removeAccess(String entityTypeName, Long entityId, Long sharedWithUserId) {
        // Get entity type
        SharableEntityType entityType = getSharableEntityType(entityTypeName);
        if (entityType == null) {
            return;
        }

        entityAccessRepository.deleteBySharedWithUserIdAndEntityTypeIdAndEntityId(
                sharedWithUserId, entityType.getId(), entityId);
    }

    /**
     * Update an inherited access record to direct access
     */
    private void updateInheritedAccessToDirectAccess(EntityAccess existingAccess, Long sharedByUserId) {
        // Only update if the access is inherited
        if (Boolean.TRUE.equals(existingAccess.getIsInherited())) {
            // Get the shared by user
            User sharedByUser = userRepository.findById(sharedByUserId)
                    .orElseThrow(() -> new BadRequestRestException("Invalid shared by user ID: " + sharedByUserId));

            // Update the access record
            existingAccess.setSharedByUser(sharedByUser);
            existingAccess.setIsInherited(false);
            entityAccessRepository.save(existingAccess);
        }
    }

    /**
     * Get a sharable entity type by name
     */
    private SharableEntityType getSharableEntityType(String entityTypeName) {
        return sharableEntityTypeRepository.findByName(entityTypeName).orElse(null);
    }
}
