package com.enosisbd.api.server.controller.project;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.dto.CrawledPageSummaryDto;
import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.TreeNodeDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface ProjectApi {
    @GetMapping
    @Operation(summary = "Get all projects")
    RestResponse<List<ProjectDto>> getAll();

    @PostMapping
    @Operation(summary = "Create a new project")
    @ApiResponse(responseCode = "201", description = "Project created successfully")
    ResponseEntity<RestResponse<ProjectDto>> add(@Valid @RequestBody ProjectDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get project by ID")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    RestResponse<ProjectDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update project")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    RestResponse<ProjectDto> update(
            @PathVariable Long id,
            @Valid @RequestBody ProjectDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete project")
    @ApiResponse(responseCode = "204", description = "Project deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    ResponseEntity<Void> delete(@PathVariable Long id);

    @GetMapping("/{id}/modules")
    @Operation(summary = "Get versions for a modules")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    RestResponse<List<ModuleDto>> findModules(@PathVariable Long id);

    @GetMapping("/{projectId}/crawled-pages")
    @Operation(summary = "Get crawled pages for a project")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    RestResponse<List<CrawledPageDto>> findCrawledPages(@PathVariable Long projectId);

    @GetMapping("/{projectId}/crawled-pages/summary")
    @Operation(summary = "Get crawled pages summary for a project (without DOMJson for performance)")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    RestResponse<List<CrawledPageSummaryDto>> findCrawledPagesSummary(@PathVariable Long projectId);

    @GetMapping("/{id}/tree")
    @Operation(summary = "Get project tree structure including modules and submodules")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    RestResponse<TreeNodeDto> getProjectTree(@PathVariable Long id);

    @PostMapping("/{id}/refresh-google-sheet")
    @Operation(summary = "Refresh project data from Google Sheet")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    RestResponse<ProjectDto> refreshFromGoogleSheet(@PathVariable Long id);
}
