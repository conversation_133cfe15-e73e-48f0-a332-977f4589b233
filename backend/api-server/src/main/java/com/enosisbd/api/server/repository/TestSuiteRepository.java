package com.enosisbd.api.server.repository;

import java.util.List;
import java.util.Optional;


import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.enosisbd.api.server.entity.TestSuite;
import org.springframework.data.repository.query.Param;

public interface TestSuiteRepository extends BaseRepository<TestSuite, Long> {
    @Query("SELECT ts FROM TestSuite ts LEFT JOIN FETCH ts.versionEntity WHERE ts.versionEntity.id = :versionId")
    List<TestSuite> findByVersionIdJoined(Long versionId);

    @Query("SELECT ts FROM TestSuite ts LEFT JOIN FETCH ts.versionEntity")
    List<TestSuite> findAllJoined();

    @Query("SELECT ts FROM TestSuite ts LEFT JOIN FETCH ts.versionEntity WHERE ts.id = :id")
    Optional<TestSuite> findByIdJoined(Long id);

    @Query("SELECT COUNT(ts) FROM TestSuite ts WHERE ts.versionEntity.id = :versionId")
    Integer countByVersionId(Long versionId);
}
