package com.enosisbd.api.server.service.user.impl;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.enosisbd.api.server.dto.PasswordChangeDTO;
import com.enosisbd.api.server.dto.RoleDTO;
import com.enosisbd.api.server.dto.UserDTO;
import com.enosisbd.api.server.dto.UserPreferenceDTO;
import com.enosisbd.api.server.dto.UserProfileDTO;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.entity.UserPreference;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.repository.SharableEntityTypeRepository;
import com.enosisbd.api.server.repository.UserPreferenceRepository;
import com.enosisbd.api.server.repository.UserRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.user.UserService;

import jakarta.persistence.LockModeType;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserPreferenceRepository userPreferenceRepository;
    private final PasswordEncoder passwordEncoder;
    private final SharableEntityTypeRepository sharableEntityTypeRepository;
    private final AuthorizationService authorizationService;

    @Override
    @Lock(LockModeType.PESSIMISTIC_READ)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmailIgnoreCase(email);
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public User persist(User user) {
        if (user.getId() != null && userRepository.findActiveById(user.getId()).isPresent()) {
            return userRepository.persist(user);
        } else {
            return userRepository.persist(user);
        }
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_READ)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public ResponseEntity<List<UserDTO>> listNonAdminUser() {
        List<User> userList = userRepository.findAllNonAdminUsers();
        List<UserDTO> dtoList = userList.stream()
                .map(this::mapToUserDTO)
                .toList();
        return ResponseEntity.ok(dtoList);
    }

    public UserDTO mapToUserDTO(User user) {
        Set<RoleDTO> roles = user.getRoles().stream()
                .map(role -> {
                    RoleDTO dto = new RoleDTO();
                    dto.setId(role.getId());
                    dto.setName(role.getName());
                    return dto;
                })
                .collect(Collectors.toSet());
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setFullName(user.getFullName());
        dto.setEmail(user.getEmail());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        dto.setApproved(user.getApproved());
        dto.setRoles(roles);
        return dto;
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_READ)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public UserProfileDTO getCurrentUserProfile() {
        String currentUsername = authorizationService.getCurrentUsername();
        User user = userRepository.findByEmailIgnoreCase(currentUsername)
                .orElseThrow(() -> new NotFoundRestException("User not found"));

        return UserProfileDTO.builder()
                .id(user.getId())
                .fullName(user.getFullName())
                .email(user.getEmail())
                .build();
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public UserProfileDTO updateUserProfile(UserProfileDTO profileDTO) {
        String currentUsername = authorizationService.getCurrentUsername();
        User user = userRepository.findByEmailIgnoreCase(currentUsername)
                .orElseThrow(() -> new NotFoundRestException("User not found"));

        // Update user profile
        user.setFullName(profileDTO.getFullName());

        // Save the updated user
        user = userRepository.persist(user);

        return UserProfileDTO.builder()
                .id(user.getId())
                .fullName(user.getFullName())
                .email(user.getEmail())
                .build();
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public ResponseEntity<?> changePassword(PasswordChangeDTO passwordChangeDTO) {
        // Validate passwords match
        if (!passwordChangeDTO.getNewPassword().equals(passwordChangeDTO.getConfirmPassword())) {
            throw new BadRequestRestException("New password and confirm password do not match");
        }

        String currentUsername = authorizationService.getCurrentUsername();
        User user = userRepository.findByEmailIgnoreCase(currentUsername)
                .orElseThrow(() -> new NotFoundRestException("User not found"));

        // Validate current password
        if (!passwordEncoder.matches(passwordChangeDTO.getCurrentPassword(), user.getPassword())) {
            throw new BadRequestRestException("Current password is incorrect");
        }

        // Update password
        user.setPassword(passwordEncoder.encode(passwordChangeDTO.getNewPassword()));
        userRepository.persist(user);

        return ResponseEntity.ok(Map.of("message", "Password updated successfully"));
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_READ)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public UserPreferenceDTO getUserPreference() {
        String currentUsername = authorizationService.getCurrentUsername();
        User user = userRepository.findByEmailIgnoreCase(currentUsername)
                .orElseThrow(() -> new NotFoundRestException("User not found"));

        // Get or create user preference
        UserPreference preference = userPreferenceRepository.findByUserId(user.getId())
                .orElseGet(() -> {
                    UserPreference newPreference = new UserPreference();
                    newPreference.setUser(user);
                    newPreference.setEntityViewMode("card"); // Default to card view
                    return userPreferenceRepository.persist(newPreference);
                });

        return UserPreferenceDTO.builder()
                .id(preference.getId())
                .entityViewMode(preference.getEntityViewMode())
                .build();
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public UserPreferenceDTO updateUserPreference(UserPreferenceDTO preferenceDTO) {
        String currentUsername = authorizationService.getCurrentUsername();
        User user = userRepository.findByEmailIgnoreCase(currentUsername)
                .orElseThrow(() -> new NotFoundRestException("User not found"));

        // Get or create user preference
        UserPreference preference = userPreferenceRepository.findByUserId(user.getId())
                .orElseGet(() -> {
                    UserPreference newPreference = new UserPreference();
                    newPreference.setUser(user);
                    return newPreference;
                });

        // Update preference
        preference.setEntityViewMode(preferenceDTO.getEntityViewMode());

        // Save the updated preference
        preference = userPreferenceRepository.persist(preference);

        return UserPreferenceDTO.builder()
                .id(preference.getId())
                .entityViewMode(preference.getEntityViewMode())
                .build();
    }

    @Override
    @Lock(LockModeType.PESSIMISTIC_READ)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public List<User> searchUsersForSharing(String query, Long currentUserId, String entityType, Long entityId) {
        // If entityType and entityId are provided, filter out users who already have access
        if (entityType != null && entityId != null) {
            // Get entity type ID
            Long entityTypeId = sharableEntityTypeRepository.findByName(entityType.toUpperCase())
                    .map(type -> type.getId())
                    .orElse(null);

            if (entityTypeId != null) {
                return userRepository.searchForSharing(query, currentUserId, entityTypeId, entityId);
            }
        }

        // Otherwise, just search by name or email
        return userRepository.searchByNameOrEmail(query);
    }
}
