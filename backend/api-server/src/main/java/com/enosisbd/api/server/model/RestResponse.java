package com.enosisbd.api.server.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RestResponse<T> {
    private T data;
    private String errorMessage;
    private boolean success = true;

    public RestResponse(T data) {
        this.data = data;
        this.success = true;
    }

    public static <T> RestResponse<T> of(T data) {
        return new RestResponse<>(data);
    }

    public static <T> RestResponse<T> error(String errorMessage) {
        RestResponse<T> response = new RestResponse<>();
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }
}
