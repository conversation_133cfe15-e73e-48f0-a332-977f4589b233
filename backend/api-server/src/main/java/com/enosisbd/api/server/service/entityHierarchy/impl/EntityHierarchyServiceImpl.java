package com.enosisbd.api.server.service.entityHierarchy.impl;

import com.enosisbd.api.server.entity.*;
import com.enosisbd.api.server.entity.Module;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.*;
import com.enosisbd.api.server.service.entityHierarchy.EntityHierarchyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Service for managing entity hierarchy relationships.
 * This provides a centralized place for defining and navigating the entity hierarchy.
 */
@Service
@RequiredArgsConstructor
public class EntityHierarchyServiceImpl implements EntityHierarchyService {

    private final ProjectRepository projectRepository;
    private final ModuleRepository moduleRepository;
    private final SubModuleRepository subModuleRepository;
    private final TestScriptRepository testScriptRepository;
    private final CrawledPageRepository crawledPageRepository;

    /**
     * Get the parent entity type for a given entity type
     * @param entityType The entity type to get the parent for
     * @return The parent entity type, or null if the entity has no parent
     */
    @Override
    public EntityType getParentEntityType(EntityType entityType) {
        if (entityType == null) {
            return null;
        }

        switch (entityType) {
            case MODULE:
                return EntityType.PROJECT;
            case SUBMODULE:
                return EntityType.MODULE;
            case TEST_SCRIPT:
                return EntityType.SUBMODULE;
            case CRAWLED_PAGE:
                return EntityType.PROJECT;
            default:
                return null;
        }
    }

    /**
     * Get the parent entity ID for a given entity
     * @param entityType The type of the entity
     * @param entityId The ID of the entity
     * @return A map containing the parent entity type and ID, or null if the entity has no parent
     */
    @Override
    public Map.Entry<EntityType, Long> getParentEntity(EntityType entityType, Long entityId) {
        if (entityType == null || entityId == null) {
            return null;
        }

        switch (entityType) {
            case MODULE:
                Module module = moduleRepository.findById(entityId)
                        .orElseThrow(() -> new BadRequestRestException("Module not found with ID: " + entityId));
                return new AbstractMap.SimpleEntry<>(EntityType.PROJECT, module.getProject().getId());

            case SUBMODULE:
                SubModule subModule = subModuleRepository.findById(entityId)
                        .orElseThrow(() -> new BadRequestRestException("SubModule not found with ID: " + entityId));
                return new AbstractMap.SimpleEntry<>(EntityType.MODULE, subModule.getModule().getId());

            case TEST_SCRIPT:
                TestScript testScript = testScriptRepository.findById(entityId)
                        .orElseThrow(() -> new BadRequestRestException("Test script not found with ID: " + entityId));
                return new AbstractMap.SimpleEntry<>(EntityType.SUBMODULE, testScript.getSubModule().getId());

            case CRAWLED_PAGE:
                CrawledPage crawledPage = crawledPageRepository.findById(entityId)
                        .orElseThrow(() -> new BadRequestRestException("Crawled page not found with ID: " + entityId));
                return new AbstractMap.SimpleEntry<>(EntityType.PROJECT, crawledPage.getProject().getId());

            default:
                return null;
        }
    }

    /**
     * Get the creator of an entity
     * @param entityType The type of the entity
     * @param entityId The ID of the entity
     * @return The creator's username, or null if not found
     */
    @Override
    public String getEntityCreator(EntityType entityType, Long entityId) {
        if (entityType == null || entityId == null) {
            return null;
        }

        switch (entityType) {
            case PROJECT:
                return projectRepository.findById(entityId)
                        .map(Project::getCreatedBy)
                        .orElse(null);
            case MODULE:
                return moduleRepository.findById(entityId)
                        .map(Module::getCreatedBy)
                        .orElse(null);
            case SUBMODULE:
                return subModuleRepository.findById(entityId)
                        .map(SubModule::getCreatedBy)
                        .orElse(null);
            case TEST_SCRIPT:
                return testScriptRepository.findById(entityId)
                        .map(TestScript::getCreatedBy)
                        .orElse(null);
            case CRAWLED_PAGE:
                return crawledPageRepository.findById(entityId)
                        .map(CrawledPage::getCreatedBy)
                        .orElse(null);
            default:
                return null;
        }
    }

    /**
     * Get all child entities of a given entity
     * @param entityType The type of the parent entity
     * @param entityId The ID of the parent entity
     * @return A list of maps containing the child entity types and IDs
     */
    @Override
    public List<Map.Entry<EntityType, Long>> getChildEntities(EntityType entityType, Long entityId) {
        if (entityType == null || entityId == null) {
            return Collections.emptyList();
        }

        List<Map.Entry<EntityType, Long>> children = new ArrayList<>();

        switch (entityType) {
            case PROJECT:
                // Add modules
                List<Module> modules = moduleRepository.findByProjectId(entityId);
                modules.forEach(module ->
                    children.add(new AbstractMap.SimpleEntry<>(EntityType.MODULE, module.getId())));

                // Add crawled pages
                List<CrawledPage> projectCrawledPages = crawledPageRepository.findByProjectId(entityId);
                projectCrawledPages.forEach(crawledPage ->
                    children.add(new AbstractMap.SimpleEntry<>(EntityType.CRAWLED_PAGE, crawledPage.getId())));
                break;

            case MODULE:
                // Add submodules
                List<SubModule> subModules = subModuleRepository.findByModuleId(entityId);
                subModules.forEach(subModule ->
                    children.add(new AbstractMap.SimpleEntry<>(EntityType.SUBMODULE, subModule.getId())));
                break;

            case SUBMODULE:
                // Add test scripts
                List<TestScript> testScripts = testScriptRepository.findBySubModuleId(entityId);
                testScripts.forEach(testScript ->
                    children.add(new AbstractMap.SimpleEntry<>(EntityType.TEST_SCRIPT, testScript.getId())));
                break;
        }

        return children;
    }

    /**
     * Check if an entity exists
     * @param entityType The type of the entity
     * @param entityId The ID of the entity
     * @return true if the entity exists, false otherwise
     */
    @Override
    public boolean entityExists(EntityType entityType, Long entityId) {
        if (entityType == null || entityId == null) {
            return false;
        }

        switch (entityType) {
            case PROJECT:
                return projectRepository.existsById(entityId);
            case MODULE:
                return moduleRepository.existsById(entityId);
            case SUBMODULE:
                return subModuleRepository.existsById(entityId);
            case TEST_SCRIPT:
                return testScriptRepository.existsById(entityId);
            case CRAWLED_PAGE:
                return crawledPageRepository.existsById(entityId);
            default:
                return false;
        }
    }
}
