package com.enosisbd.api.server.service.passwordReset;

import com.enosisbd.api.server.entity.User;
import org.springframework.transaction.annotation.Transactional;

public interface PasswordResetService {
    void createPasswordResetTokenForUser(User user);
    String validatePasswordResetToken(String token);
    void changeUserPassword(User user, String newPassword);
    void markTokenAsUsed(String token);
    User getUserByPasswordResetToken(String token);
}
