package com.enosisbd.api.server.controller.sharing;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.ShareEntityRequest;
import com.enosisbd.api.server.dto.UserDTO;
import com.enosisbd.api.server.model.RestResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface SharingApi {
    @GetMapping("/{entityType}/{entityId}/users")
    @RequiresEntityAccess(isDynamic = true)
    RestResponse<List<UserDTO>> getUsersWithAccess(
            @PathVariable String entityType,
            @PathVariable Long entityId);

    @PostMapping("/{entityType}/{entityId}/share")
    @RequiresEntityAccess(isDynamic = true)
    RestResponse<Boolean> shareEntity(
            @PathVariable String entityType,
            @PathVariable Long entityId,
            @Valid @RequestBody ShareEntityRequest request);

    @DeleteMapping("/{entityType}/{entityId}/unshare/{userId}")
    @RequiresEntityAccess(isDynamic = true)
    RestResponse<Boolean> removeAccess(
            @PathVariable String entityType,
            @PathVariable Long entityId,
            @PathVariable Long userId);

    @GetMapping("/users/search")
    RestResponse<List<UserDTO>> searchUsers(
            @RequestParam String query,
            @RequestParam(required = false) String entityType,
            @RequestParam(required = false) Long entityId);
}
