package com.enosisbd.api.server.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.servers.Server;

@Configuration
@OpenAPIDefinition(
        info = @Info(
                title = "TAP API Server",
                version = "1.0.0",
                description = "TAP is a cutting-edge microservice built to streamline and enhance test automation workflows with exceptional speed and precision. Whether you're running unit tests, integration tests, or end-to-end scenarios, this service ensures reliable execution, reporting, and continuous integration support to help you maintain high-quality software with ease.",
                termsOfService = ""
        ),
        servers = {
                @Server(url = "${app.backend.url}", description = "Development Server")
        }, security = @SecurityRequirement(name = "JWT")
)
@SecurityScheme(
        name = "JWT",
        type = SecuritySchemeType.HTTP,
        scheme = "bearer",
        bearerFormat = "JWT",
        in = SecuritySchemeIn.HEADER,
        description = "JWT authentication using a Bearer token"
)
public class SwaggerConfig {


    @Bean
    public GroupedOpenApi authenticationApi() {
        return GroupedOpenApi.builder()
                .pathsToMatch("/api/auth/**", "/.well-known/jwks.json")
                .group("Authentication Api")
                .build();
    }

    @Bean
    public GroupedOpenApi apiServerApi() {
        return GroupedOpenApi.builder()
                .pathsToMatch("/**")
                .group("API Server API")
                .build();
    }

}
