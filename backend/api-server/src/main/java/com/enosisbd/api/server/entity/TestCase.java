package com.enosisbd.api.server.entity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.hibernate.type.SqlTypes;

import com.fasterxml.jackson.annotation.JsonBackReference;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;

@Entity
@Table(indexes = {
    @Index(name = "idx_test_case_test_suite_id", columnList = "test_suite_id")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TestCase extends BaseEntity {

    @NotNull(message = "Test case ID cannot be null")
    @NotBlank(message = "Test case ID cannot be empty")
    @Column(nullable = false)
    private String testCaseId;

    @NotNull(message = "Description cannot be null")
    @Column(columnDefinition = "TEXT")
    private String description;

    @NotNull(message = "Steps cannot be null")
    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<String> steps;

    @NotNull(message = "Expected result cannot be null")
    @Column(columnDefinition = "TEXT")
    private String expectedResult;

    @Enumerated(EnumType.STRING)
    private TestCaseStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "test_suite_id")
    @JsonBackReference
    private TestSuite testSuite;

    public Long getTestSuiteId() {
        return Optional.ofNullable(testSuite)
                .map(TestSuite::getId)
                .orElse(null);
    }

    /**
     * Updates the test case status and related fields
     * @param status New status to set
     * @param comments Optional comments about the status change
     */
    public void updateStatus(TestCaseStatus status, String comments) {
        this.status = status;
        this.setUpdatedAt(LocalDateTime.now());
    }
}
