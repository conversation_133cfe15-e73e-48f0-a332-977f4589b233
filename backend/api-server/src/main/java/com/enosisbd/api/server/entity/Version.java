package com.enosisbd.api.server.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(indexes = {
    @Index(name = "idx_version_updated_at", columnList = "updatedAt"),
    @Index(name = "idx_version_project", columnList = "project_id")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Version extends BaseEntity {

    @NotNull(message = "Version name cannot be null")
    @NotBlank(message = "Version name cannot be empty")
    @Size(min = 3, max = 255, message = "Version name must be between 3 and 255 characters")
    @Column(nullable = false)
    private String name;

    private String testCaseGenerationMethod;
    private String platform;
    private String language;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id")
    private Project project;

    @OneToMany(mappedBy = "versionEntity", cascade = CascadeType.ALL)
    private List<TestSuite> testSuites = new ArrayList<>();

    public Long getProjectId() {
        return Optional.ofNullable(getProject())
                .map(Project::getId)
                .orElse(null);
    }

    /**
     * Adds a test suite to this version
     * @param testSuite The test suite to add
     */
    public void addTestSuite(TestSuite testSuite) {
        testSuites.add(testSuite);
        testSuite.setVersionEntity(this);
    }

    /**
     * Removes a test suite from this version
     * @param testSuite The test suite to remove
     */
    public void removeTestSuite(TestSuite testSuite) {
        testSuites.remove(testSuite);
        testSuite.setVersionEntity(null);
    }
}
