package com.enosisbd.api.server.service.authentication.impl;

import com.enosisbd.api.server.dto.AuthenticationDTO.SignInResponseDTO;
import com.enosisbd.api.server.model.ProviderType;
import com.enosisbd.api.server.service.authentication.AuthenticationService;
import com.enosisbd.api.server.service.authentication.OAuth2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;

@Log4j2
@Service
@RequiredArgsConstructor
public class OAuth2ServiceImpl implements OAuth2Service {

    private final AuthenticationService authenticationService;
    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${spring.security.oauth2.client.registration.google.client-id}")
    private String googleClientId;

    @Value("${spring.security.oauth2.client.registration.google.redirect-uri}")
    private String googleRedirectUri;

    @Value("${spring.security.oauth2.client.registration.google.client-secret}")
    private String googleClientSecret;

    @Value("${app.frontend.url}")
    private String frontendUrl;

    @Override
    public ResponseEntity<Void> initiateGoogleLogin() {
        // Google OAuth2 authorization URL
        String googleAuthUrl = "https://accounts.google.com/o/oauth2/auth";
        
        // Build the authorization URL with required parameters
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(googleAuthUrl)
                .queryParam("client_id", googleClientId)
                .queryParam("redirect_uri", googleRedirectUri)
                .queryParam("response_type", "code")
                .queryParam("scope", "email profile https://www.googleapis.com/auth/spreadsheets.readonly")
                .queryParam("access_type", "offline");
        
        // Redirect to Google's authorization page
        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(builder.toUriString()));
        
        return new ResponseEntity<>(headers, HttpStatus.FOUND);
    }

    @Override
    public ResponseEntity<Void> handleGoogleCallback(String code) {
        // Check if there's an error parameter indicating cancellation
        if (code == null) {
            // Redirect to frontend login page with error
            HttpHeaders headers = new HttpHeaders();
            headers.setLocation(URI.create(frontendUrl + "/login?error=google_oauth_cancelled"));
            return new ResponseEntity<>(headers, HttpStatus.FOUND);
        }
        
        // Redirect to frontend with tokens
        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(frontendUrl + 
                "/oauth/callback?code=" + code +
                "&state=google"));

        return new ResponseEntity<>(headers, HttpStatus.FOUND);
    }

    @Override
    public ResponseEntity<SignInResponseDTO> processOAuthCallback(String code) {
        try {
            // 1. Exchange code for token
            String tokenUrl = "https://oauth2.googleapis.com/token";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> tokenRequest = new LinkedMultiValueMap<>();
            tokenRequest.add("code", code);
            tokenRequest.add("client_id", googleClientId);
            tokenRequest.add("client_secret", googleClientSecret);
            tokenRequest.add("redirect_uri", googleRedirectUri);
            tokenRequest.add("grant_type", "authorization_code");
            
            HttpEntity<MultiValueMap<String, String>> tokenEntity = new HttpEntity<>(tokenRequest, headers);
            ResponseEntity<Map> tokenResponse = restTemplate.exchange(tokenUrl, HttpMethod.POST, tokenEntity, Map.class);
            
            Map<String, Object> tokenData = tokenResponse.getBody();
            String accessToken = (String) tokenData.get("access_token");
            String refreshToken = (String) tokenData.get("refresh_token");
            Integer expiresIn = (Integer) tokenData.get("expires_in");

            // 2. Get user info using the access token
            String userInfoUrl = "https://www.googleapis.com/oauth2/v3/userinfo";
            headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);

            HttpEntity<String> userInfoRequest = new HttpEntity<>(headers);
            ResponseEntity<Map> userResponse = restTemplate.exchange(userInfoUrl, HttpMethod.GET, userInfoRequest, Map.class);

            Map<String, Object> attributes = userResponse.getBody();

            // 3. Process the login with our authentication service and store tokens
            return authenticationService.processOAuth2Login(attributes, ProviderType.GOOGLE.getName(),
                    accessToken, refreshToken, expiresIn);
        } catch (Exception e) {
            log.error("OAuth2 callback processing failed", e);
            HttpHeaders headers = new HttpHeaders();
            headers.setLocation(URI.create(frontendUrl + "/login?error=oauth_failure"));
            return new ResponseEntity<>(headers, HttpStatus.FOUND);
        }
    }
}
