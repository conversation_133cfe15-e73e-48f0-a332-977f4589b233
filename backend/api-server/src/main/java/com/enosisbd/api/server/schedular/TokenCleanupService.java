package com.enosisbd.api.server.schedular;

import com.enosisbd.api.server.repository.PasswordResetTokenRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class TokenCleanupService {
    
    @Autowired
    private PasswordResetTokenRepository tokenRepository;
    
    @Scheduled(cron = "0 0 3 * * ?") // Runs daily at 3 AM
    public void cleanupUsedTokens() {
        LocalDateTime cutoff = LocalDateTime.now().minusDays(1);
        tokenRepository.deleteByUsedTrueOrExpiryDateBefore(cutoff);
    }
}