package com.enosisbd.api.server.service.module.impl;

import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.entity.Module;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ModuleRepository;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.module.ModuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ModuleServiceImpl implements ModuleService {
    private final ModuleRepository repository;
    private final ProjectRepository projectRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<ModuleDto> findAll() {
        List<Module> modules = repository.findAll();
        return entitySharingService.getAccessibleEntities(modules, EntityType.MODULE)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ModuleDto> getById(Long id) {
        return repository.findByIdJoined(id)
                .map(this::convertToDto);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public ModuleDto add(ModuleDto dto) {
        Module entity = convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long projectId = dto.getProjectId();
        if (projectId != null) {
            // Check if user has access to the project
            var project = projectRepository
                    .findById(projectId)
                    .orElseThrow(() -> BadRequestRestException.with("Project not found with ID: " + projectId));

            if (!entitySharingService.hasAccess(project, EntityType.PROJECT)) {
                throw new BadRequestRestException("You don't have access to this project");
            }

            entity.setProject(project);
        }

        repository.save(entity);
        return convertToDto(entity);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ModuleDto> update(ModuleDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        Module existingEntity = maybe.get();
        Module entity = convertToEntity(dto);

        // Check if project ID is valid and matches the existing entity
        Long projectId = dto.getProjectId();
        if (projectId != null && !projectId.equals(existingEntity.getProjectId())) {
            throw new BadRequestRestException("Cannot change the project of a module");
        }

        // Set the project from the existing entity
        entity.setProject(existingEntity.getProject());

        repository.save(entity);
        return Optional.of(convertToDto(entity));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        if (!repository.existsById(id)) return Optional.empty();
        repository.deleteById(id);
        return Optional.of(true);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<ModuleDto> findByProjectId(Long projectId) {
        return repository.findByProjectIdJoined(projectId)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Delete all modules for a project
     *
     * @param projectId the project id
     * @return true if modules were deleted, false otherwise
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean deleteByProjectId(Long projectId) {
        List<Module> modules = repository.findByProjectId(projectId);
        if (modules.isEmpty()) {
            return false;
        }

        // Delete all modules
        repository.deleteAll(modules);
        return true;
    }

    private ModuleDto convertToDto(Module entity) {
        ModuleDto dto = new ModuleDto();
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setName(entity.getName());
        dto.setProjectId(entity.getProjectId());
        return dto;
    }

    private Module convertToEntity(ModuleDto dto) {
        Module entity = new Module();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        return entity;
    }
}
