package com.enosisbd.api.server.service.passwordReset.impl;

import com.enosisbd.api.server.entity.PasswordResetToken;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.repository.PasswordResetTokenRepository;
import com.enosisbd.api.server.repository.UserRepository;
import com.enosisbd.api.server.service.passwordReset.PasswordResetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Log4j2
@Service
@RequiredArgsConstructor
public class PasswordResetServiceImpl implements PasswordResetService {

    private final UserRepository userRepository;
    private final PasswordResetTokenRepository tokenRepository;
    //    private final JavaMailSender mailSender;
    private final PasswordEncoder passwordEncoder;
//
//    @Value("${app.frontend.url}")
//    private String frontendUrl;

    @Override
    @Transactional
    public void createPasswordResetTokenForUser(User user) {
        PasswordResetToken existingToken = tokenRepository.findByUser(user);
        if (existingToken != null && !existingToken.isExpired()) {
            return;
        } else if (existingToken != null) {
            tokenRepository.delete(existingToken);
        }
        String token = UUID.randomUUID().toString();
        PasswordResetToken resetToken = new PasswordResetToken(token, user);
        tokenRepository.persist(resetToken);
        sendPasswordResetEmail(user.getEmail(), token);
    }


    private void sendPasswordResetEmail(String email, String token) {
        /*try {
            String resetUrl = frontendUrl + "/reset-password?token=" + token;
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(email);
            helper.setSubject("Password Reset Request");
            helper.setText("Click the link below to reset your password:\n" + resetUrl +
                    "\n\nThis link is valid for 24 hours.");

            mailSender.send(message);
        } catch (Exception e) {
            log.error("Failed to send password reset email to {}", email, e);
        }*/
    }

    @Override
    public String validatePasswordResetToken(String token) {
        PasswordResetToken passToken = tokenRepository.findByToken(token);
        if (passToken == null) return "invalid";
        if (passToken.isUsed()) return "used";
        if (passToken.isExpired()) return "expired";
        return "valid";
    }

    @Override
    @Transactional
    public void changeUserPassword(User user, String newPassword) {
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.persist(user);
    }

    @Override
    @Transactional
    public void markTokenAsUsed(String token) {
        PasswordResetToken passToken = tokenRepository.findByToken(token);
        if (passToken != null) {
            passToken.setUsed(true);
            tokenRepository.persist(passToken);
        }
    }

    @Override
    public User getUserByPasswordResetToken(String token) {
        PasswordResetToken tokenEntity = tokenRepository.findByToken(token);
        return tokenEntity != null ? tokenEntity.getUser() : null;
    }
}