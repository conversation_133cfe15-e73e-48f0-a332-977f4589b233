package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.PasswordResetToken;
import com.enosisbd.api.server.entity.User;
import io.hypersistence.utils.spring.repository.BaseJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface PasswordResetTokenRepository extends BaseJpaRepository<PasswordResetToken, Long> {
    PasswordResetToken findByToken(String token);
    PasswordResetToken findByUser(User user);

    @Modifying
    @Query("DELETE FROM PasswordResetToken t WHERE t.used = true OR t.expiryDate < :cutoff")
    void deleteByUsedTrueOrExpiryDateBefore(LocalDateTime cutoff);
}
