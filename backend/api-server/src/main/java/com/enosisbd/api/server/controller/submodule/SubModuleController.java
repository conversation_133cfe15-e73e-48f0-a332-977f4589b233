package com.enosisbd.api.server.controller.submodule;

import com.enosisbd.api.server.dto.SheetTestCasesResponseDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.dto.TestScriptDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import com.enosisbd.api.server.service.testScript.TestScriptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Optional;

@RestController
@RequestMapping("/api/submodules")
@RequiredArgsConstructor
@Tag(name = "SubModule Controller", description = "APIs for submodule management")
public class SubModuleController implements SubModuleApi {

    private final SubModuleService subModuleService;
    private final TestScriptService testScriptService;
    @PostMapping
    @Operation(summary = "Create a new submodule")
    @ApiResponse(responseCode = "201", description = "SubModule created successfully")
    @Override
    public ResponseEntity<RestResponse<SubModuleDto>> add(@Valid @RequestBody SubModuleDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(subModuleService.add(dto)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get submodule by ID")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    @Override
    public RestResponse<SubModuleDto> getById(@PathVariable Long id) {
        return subModuleService.getById(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("SubModule not found with ID: " + id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update submodule")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    @Override
    public RestResponse<SubModuleDto> update(
            @PathVariable Long id,
            @Valid @RequestBody SubModuleDto dto) {
        dto.setId(id);
        return subModuleService.update(dto)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("SubModule not found with ID: " + id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete submodule")
    @ApiResponse(responseCode = "204", description = "SubModule deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        return subModuleService.delete(id)
                .map(ignored -> ResponseEntity.noContent().<Void>build())
                .orElseThrow(() -> NotFoundRestException.with("SubModule not found with ID: " + id));
    }

    @GetMapping("/{id}/test-script")
    @Operation(summary = "Get test scripts for a submodule")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    @Override
    public RestResponse<TestScriptDto> findBySubModuleId(@PathVariable Long id) {
        return testScriptService.findBySubModuleId(id)
                .map(testScriptDto -> RestResponse.of(testScriptDto))
                .orElseThrow(() -> NotFoundRestException.with("TestScript not found with SubModule ID: " + id));
    }

    @Override
    @GetMapping("/{id}/test-cases")
    @Operation(summary = "Generate test cases for a submodule from Google Sheets")
    @ApiResponse(responseCode = "200", description = "Test cases generated successfully")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    public RestResponse<SheetTestCasesResponseDto> generateTestCases(@PathVariable Long id)
            throws IOException, GeneralSecurityException {
        return RestResponse.of(subModuleService.generateTestCases(id));
    }
}
