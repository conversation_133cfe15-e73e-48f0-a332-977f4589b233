package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.Module;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ModuleRepository extends BaseRepository<Module, Long> {
    
    @Query("from Module m left join fetch m.project where m.id = :id")
    Optional<Module> findByIdJoined(Long id);
    
    @Query("from Module m left join fetch m.project p where p.id = :projectId")
    List<Module> findByProjectIdJoined(Long projectId);
    
    @Query("from Module m where m.project.id = :projectId")
    List<Module> findByProjectId(Long projectId);

}
