package com.enosisbd.api.server.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for representing the result of submodule extraction with validation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmoduleExtractionResultDto {
    private List<SubModuleRangeDto> submodules;
    private List<SubmoduleValidationIssueDto> validationIssues;
    
    public boolean hasValidationIssues() {
        return validationIssues != null && !validationIssues.isEmpty();
    }
}
