package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.Version;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VersionRepository extends BaseRepository<Version, Long> {
    @Query("from Version v left join fetch v.project order by v.updatedAt desc")
    List<Version> findAllJoined();

    @Query("from Version v left join fetch v.project where v.id = :id")
    Optional<Version> findByIdJoined(Long id);

    @Query("from Version v left join fetch v.project p where p.id = :projectId")
    List<Version> findByProjectIdJoined(Long projectId);

    @Query("from Version v where v.project.id = :projectId")
    List<Version> findAllByProjectId(Long projectId);

    @Query("from Version v where v.project.id = :projectId")
    List<Version> findByProjectId(@Param("projectId") Long projectId);

    boolean existsByIdAndCreatedBy(Long id, String createdBy);

    @Query("from Version v left join fetch v.project where v.createdBy = :createdBy order by v.updatedAt desc")
    List<Version> findByCreatedByJoined(@Param("createdBy") String createdBy);

    @Query("from Version v left join fetch v.project p where p.id = :projectId and v.createdBy = :createdBy order by v.updatedAt desc")
    List<Version> findByProjectIdAndCreatedByJoined(
            @Param("projectId") Long projectId,
            @Param("createdBy") String createdBy
    );


}