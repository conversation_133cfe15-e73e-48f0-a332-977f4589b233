package com.enosisbd.api.server.service.reports.impl;

import com.enosisbd.api.server.dto.ReportsDTOs;
import com.enosisbd.api.server.entity.*;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.service.reports.ReportsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * The type Reports service.
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class ReportsServiceImpl implements ReportsService {

    private final ProjectRepository projectRepository;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public List<ReportsDTOs.ReportsResponseDTO> findReports(Long projectId, Long versionId) {
        if (projectId == null || versionId == null || projectId <= 0 || versionId <= 0) {
            return Collections.emptyList();
        }
        log.info("Fetching reports for projectId: {}, versionId: {}", projectId, versionId);

        Optional<Project> optionalProject = projectRepository.findById(projectId);

        if (optionalProject.isEmpty()) {
            log.warn("Project not found with id: {}", projectId);
            return Collections.emptyList();
        }

        Project project = optionalProject.get();

        // Find the specific version (we're not supporting multiple versions now)
        Version version = project.getVersions().stream()
                .filter(v -> v.getId().equals(versionId))
                .findFirst()
                .orElse(null);

        if (version == null) {
            log.warn("Version not found with id: {} within project id: {}", versionId, projectId);
            return Collections.emptyList();
        }

        List<ReportsDTOs.ReportsResponseDTO> reportsResponseDTOS = new ArrayList<>();
        List<TestSuite> testSuites = version.getTestSuites();

        if (testSuites == null || testSuites.isEmpty()) {
            log.info("No test suites found for version id: {}", versionId);
            return Collections.emptyList();
        }

        for (TestSuite testSuite : testSuites) {
            int passedCount = 0;
            int failedCount = 0;
            int notExecutedCount = 0;

            List<TestCase> testCases = testSuite.getTestCases();
            int totalTestCases = (testCases != null) ? testCases.size() : 0;

            if (testCases != null) {
                for (TestCase testCase : testCases) {
                    TestCaseStatus status = testCase.getStatus();

                    if (status == TestCaseStatus.PASSED) {
                        passedCount++;
                    } else if (status == TestCaseStatus.FAILED) {
                        failedCount++;
                    } else {
                        notExecutedCount++;
                    }
                }
            } else {
                log.warn("Test suite '{}' (id: {}) has a null list of test cases.", testSuite.getTitle(), testSuite.getId());
            }

            ReportsDTOs.ReportsResponseDTO.ReportTestStatus reportTestStatus = ReportsDTOs.ReportsResponseDTO.ReportTestStatus.builder()
                    .passed(passedCount)
                    .failed(failedCount)
                    .notExecuted(notExecutedCount)
                    .build();

            ReportsDTOs.ReportsResponseDTO reportDTO = ReportsDTOs.ReportsResponseDTO.builder()
                    .testSuiteId(testSuite.getId())
                    .testSuiteName(testSuite.getTitle())
                    .numberOfTestCases(totalTestCases)
                    .reportTestStatus(reportTestStatus)
                    .build();

            reportsResponseDTOS.add(reportDTO);
        }

        log.info("Successfully generated {} report entries for version id: {}", reportsResponseDTOS.size(), versionId);
        return reportsResponseDTOS;
    }
}