package com.enosisbd.api.server.util;

import com.enosisbd.api.server.service.security.TokenEncryptionService;

/**
 * Utility class to generate encryption keys for OAuth token security
 */
public class EncryptionKeyGenerator {

    public static void main(String[] args) {
        System.out.println("=== OAuth Token Encryption Key Generator ===");
        System.out.println();

        String encryptionKey = TokenEncryptionService.generateEncryptionKey();

        System.out.println("Generated AES-256 Encryption Key:");
        System.out.println(encryptionKey);
        System.out.println();

        System.out.println("Environment Variable Setup:");
        System.out.println("export app.security.token.encryption.key=" + encryptionKey);
        System.out.println();

        System.out.println("SECURITY NOTES:");
        System.out.println("1. Keep this key secure and never commit to version control");
        System.out.println("2. Use different keys for different environments");
        System.out.println("3. Store in secure environment variables");
        System.out.println("4. Backup securely - losing key means losing all tokens");
    }
}
