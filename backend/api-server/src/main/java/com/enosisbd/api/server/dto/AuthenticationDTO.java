package com.enosisbd.api.server.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

public class AuthenticationDTO {

    @Data
    @Jacksonized
    @Builder
    public static class SignInDTO {
        @NotEmpty(message = "Username cannot be empty")
        private String username;

        @NotEmpty(message = "Password cannot be empty")
        private String password;
    }

    @Data
    @Jacksonized
    @Builder
    public static class SignInResponseDTO {
        private String accessToken;
        private String refreshToken;
        private String tokenType;
    }

    @Data
    @Jacksonized
    @Builder
    public static class SignUpDTO {
        @NotEmpty(message = "Full name cannot be empty")
        @Size(min = 4, message = "Full name must have at least 4 characters")
        private String fullName;

        @NotEmpty(message = "Email cannot be empty")
        @Email(message = "Email should be valid")
        private String username;

        @NotEmpty(message = "Password cannot be empty")
        @Pattern(
                regexp = "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$",
                message = "Password must be at least 8 characters long and include a mix of uppercase, lowercase, number, and special character"
        )
        private String password;
    }

    @Data
    @Jacksonized
    @Builder
    public static class SignUpResponseDTO {
        private String userId;
        private String message;
    }

    @Data
    @Jacksonized
    @Builder
    public static class RefreshTokenDTO {
        @NotEmpty
        private String refreshToken;
    }

    @Data
    @Jacksonized
    @Builder
    public static class RefreshTokenResponseDTO {
        private String accessToken;
        private String refreshToken;
    }

    @Value
    @Jacksonized
    @Builder
    public static class ResetPasswordDTO {
        @NotBlank(message = "Password reset token must not be blank")
        String passwordResetToken;

        @NotBlank(message = "New password must not be blank")
        @Size(min = 8, max = 64, message = "Password must be between 8 and 64 characters")
        String newPassword;
    }

    @Data
    @Jacksonized
    @Builder
    public static class ResetPasswordResponseDTO {
        private boolean success;
        private String message;
    }

    @Data
    @Jacksonized
    @Builder
    public static class ForgotPasswordDTO {
        @NotEmpty(message = "Email cannot be empty")
        @Email(message = "Email should be valid")
        private String email;
    }

    @Data
    @Jacksonized
    @Builder
    public static class ForgotPasswordResponseDTO {
        private boolean success;
        private String message;
    }
} 
