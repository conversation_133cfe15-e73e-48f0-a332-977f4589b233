package com.enosisbd.api.server.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.enosisbd.api.server.entity.TestScript;

@Repository
public interface TestScriptRepository extends BaseRepository<TestScript, Long> {
    @Query("from TestScript ts left join fetch ts.subModule where ts.id = :id")
    Optional<TestScript> findByIdJoined(Long id);

    @Query("from TestScript ts left join fetch ts.subModule sm where sm.id = :subModuleId")
    Optional<TestScript> findBySubModuleIdJoined(Long subModuleId);

    @Query("from TestScript ts where ts.subModule.id = :subModuleId")
    List<TestScript> findBySubModuleId(Long subModuleId);
}