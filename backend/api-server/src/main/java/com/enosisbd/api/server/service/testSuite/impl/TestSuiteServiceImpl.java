package com.enosisbd.api.server.service.testSuite.impl;

import com.enosisbd.api.server.dto.TestSuiteDto;
import com.enosisbd.api.server.entity.TestSuite;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.TestCaseRepository;
import com.enosisbd.api.server.repository.TestSuiteRepository;
import com.enosisbd.api.server.repository.VersionRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.testSuite.TestSuiteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import com.enosisbd.api.server.entity.Version;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class TestSuiteServiceImpl implements TestSuiteService {
    private final TestSuiteRepository repository;
    private final VersionRepository versionRepository;
    private final TestCaseRepository testCaseRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        Optional<TestSuite> testSuite = repository.findById(id);
        if (testSuite.isEmpty()) {
            return false;
        }

        return entitySharingService.hasAccess(testSuite.get(), EntityType.TEST_SUITE);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestSuiteDto> getById(Long id) {
        // Access check is handled by the @RequiresEntityAccess annotation
        return repository.findByIdJoined(id)
                .map(this::convertToDto);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public TestSuiteDto add(TestSuiteDto dto) {
        validateTestSuite(dto);
        TestSuite entity = convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long versionId = dto.getVersionId();
        if (versionId != null) {
            // Check if user has access to the version
            Version version = versionRepository
                    .findById(versionId)
                    .orElseThrow(() -> BadRequestRestException.with("Version not found with ID: " + versionId));

            if (!entitySharingService.hasAccess(version, EntityType.VERSION)) {
                throw new BadRequestRestException("You don't have access to this version");
            }

            entity.setVersionEntity(version);
        }

        repository.save(entity);
        return convertToDto(entity);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestSuiteDto> update(TestSuiteDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty())
            return Optional.empty();

        TestSuite entity = convertToEntity(maybe.get(), dto);

        // Access check is handled by the @RequiresEntityAccess annotation

        // Check if version needs to be updated
        if (dto.getVersionId() != null && !Objects.equals(dto.getVersionId(), entity.getVersionId())) {
            // Check if user has access to the version
            Version version = versionRepository
                    .findById(dto.getVersionId())
                    .orElseThrow(() -> BadRequestRestException.with("Version not found with ID: " + dto.getVersionId()));
            entity.setVersionEntity(version);
        }

        repository.save(entity);
        return Optional.of(convertToDto(entity));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<TestSuite> testSuite = repository.findById(id);
        if (testSuite.isEmpty()) {
            return Optional.empty();
        }

        // Access check is handled by the @RequiresEntityAccess annotation

        // Delete the test suite
        repository.delete(testSuite.get());
        return Optional.of(true);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<TestSuiteDto> findByVersionId(Long versionId) {
        // Get all test suites for the version
        List<TestSuite> allTestSuites = repository.findByVersionIdJoined(versionId);

        // Convert to DTOs
        return allTestSuites.stream()
                .map(this::convertToDto)
                .toList();
    }

    private TestSuiteDto convertToDto(TestSuite entity) {
        TestSuiteDto dto = new TestSuiteDto();
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setTitle(entity.getTitle());
        dto.setRequirement(entity.getRequirement());
        dto.setVersionId(entity.getVersionId());

        // Set the number of test cases
        if (entity.getId() != null) {
            Integer testCaseCount = testCaseRepository.countByTestSuiteId(entity.getId());
            dto.setNumberOfTestCases(testCaseCount != null ? testCaseCount : 0);
        } else {
            dto.setNumberOfTestCases(0);
        }

        return dto;
    }

    private TestSuite convertToEntity(TestSuite entity, TestSuiteDto dto) {
        entity.setId(dto.getId());
        entity.setTitle(dto.getTitle());
        entity.setRequirement(dto.getRequirement());
        return entity;
    }

    private TestSuite convertToEntity(TestSuiteDto dto) {
        return convertToEntity(new TestSuite(), dto);
    }

    private void validateTestSuite(TestSuiteDto dto) {
        if (dto.getTitle() == null || dto.getTitle().trim().isEmpty()) {
            throw new BadRequestRestException("Title cannot be empty");
        }

        if (dto.getTitle().length() < 3 || dto.getTitle().length() > 255) {
            throw new BadRequestRestException("Title must be between 3 and 255 characters");
        }
    }
}
