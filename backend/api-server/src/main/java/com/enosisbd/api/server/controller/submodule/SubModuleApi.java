package com.enosisbd.api.server.controller.submodule;

import com.enosisbd.api.server.dto.SheetTestCasesResponseDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.dto.TestScriptDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Optional;

public interface SubModuleApi {
    @PostMapping
    @Operation(summary = "Create a new submodule")
    @ApiResponse(responseCode = "201", description = "SubModule created successfully")
    ResponseEntity<RestResponse<SubModuleDto>> add(@Valid @RequestBody SubModuleDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get submodule by ID")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    RestResponse<SubModuleDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update submodule")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    RestResponse<SubModuleDto> update(
            @PathVariable Long id,
            @Valid @RequestBody SubModuleDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete submodule")
    @ApiResponse(responseCode = "204", description = "SubModule deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    ResponseEntity<Void> delete(@PathVariable Long id);

    @GetMapping("/{id}/test-scripts")
    @Operation(summary = "Get test scripts for a submodule")
    RestResponse<TestScriptDto> findBySubModuleId(@PathVariable Long subModuleId);

    @GetMapping("/{id}/test-cases")
    @Operation(summary = "Generate test cases for a submodule from Google Sheets")
    @ApiResponse(responseCode = "200", description = "Test cases generated successfully")
    @RequiresEntityAccess(entityType = EntityType.SUBMODULE)
    RestResponse<SheetTestCasesResponseDto> generateTestCases(@PathVariable Long id)
            throws IOException, GeneralSecurityException;
}
