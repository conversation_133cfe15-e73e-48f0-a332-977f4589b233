package com.enosisbd.api.server.controller.approval;

import com.enosisbd.api.server.dto.ApprovalDTO.*;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface AdminApprovalApi {
    @PostMapping
    ResponseEntity<AdminUserApproveResponseDTO> approveOrUnapproved(@Valid @RequestBody AdminUserApproveDTO request);
}
