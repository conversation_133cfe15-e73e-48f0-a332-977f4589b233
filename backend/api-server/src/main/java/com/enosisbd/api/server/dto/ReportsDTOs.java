package com.enosisbd.api.server.dto;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

public class ReportsDTOs {

    @Data
    @Builder
    @Jacksonized
    public static class ReportsResponseDTO {
        private long testSuiteId;
        private String testSuiteName;
        private int numberOfTestCases;
        private ReportTestStatus reportTestStatus;

        @Builder
        @Data
        @Jacksonized
        public static class ReportTestStatus {
            private int passed;
            private int failed;
            private int notExecuted;
        }
    }

}
