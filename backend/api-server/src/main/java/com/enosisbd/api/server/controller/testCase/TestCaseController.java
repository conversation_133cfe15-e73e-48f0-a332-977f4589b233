package com.enosisbd.api.server.controller.testCase;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.TestCaseDto;
import com.enosisbd.api.server.dto.TestCasesListDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.testCase.TestCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/test-cases")
@RequiredArgsConstructor
@Tag(name = "Test Case Controller", description = "APIs for test case management")
public class TestCaseController implements TestCaseApi {
    private final TestCaseService service;

    @PostMapping
    @Operation(summary = "Create a new test case")
    @ApiResponse(responseCode = "201", description = "Test case created successfully")
    @Override
    public ResponseEntity<RestResponse<TestCaseDto>> add(@Valid @RequestBody TestCaseDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(service.add(dto)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get test case by ID")
    @RequiresEntityAccess(entityType = EntityType.TEST_CASE)
    @Override
    public RestResponse<TestCaseDto> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Test case not found with ID: " + id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update test case")
    @RequiresEntityAccess(entityType = EntityType.TEST_CASE)
    @Override
    public RestResponse<TestCaseDto> update(
            @PathVariable Long id,
            @Valid @RequestBody TestCaseDto dto) {
        dto.setId(id);
        return service.update(dto)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Test case not found with ID: " + id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete test case")
    @ApiResponse(responseCode = "204", description = "Test case deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.TEST_CASE)
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        return service.delete(id)
                .map(ignored -> ResponseEntity.noContent().<Void>build())
                .orElseThrow(() -> NotFoundRestException.with("Test case not found with ID: " + id));
    }

    @PostMapping("/batch")
    @Operation(summary = "Create or update multiple test cases")
    @Override
    public RestResponse<List<TestCaseDto>> createOrUpdateTestCases(
            @Valid @RequestBody TestCasesListDto testCases) {
        if (testCases.getTestCases().isEmpty()) {
            return RestResponse.of(List.of());
        }

        TestCaseDto firstTestCase = testCases.getTestCases().get(0);
        if (firstTestCase.getTestSuiteId() == null) {
            throw new NotFoundRestException("Test suite ID is required in the Test Case");
        }

        Long testSuiteId = firstTestCase.getTestSuiteId();
        // The access check for the test suite will be done in the service
        List<TestCaseDto> savedTestCases = service.saveOrUpdateList(testSuiteId, testCases.getTestCases());
        return RestResponse.of(savedTestCases);
    }
}
