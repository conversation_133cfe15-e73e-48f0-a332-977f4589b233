package com.enosisbd.api.server.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for representing the result of project processing with validation issues
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectProcessingResultDto {
    private ProjectDto project;
    private List<SubmoduleValidationIssueDto> validationIssues;
    private boolean hasValidationIssues;
    private String userGuidanceMessage;
    
    public boolean hasValidationIssues() {
        return validationIssues != null && !validationIssues.isEmpty();
    }
}
