package com.enosisbd.api.server.service.authentication.impl;

import com.enosisbd.api.server.controller.authentication.exceptions.UserIsNotActiveException;
import com.enosisbd.api.server.controller.authentication.exceptions.UsernameAlreadyExistsException;
import com.enosisbd.api.server.dto.AuthenticationDTO.*;
import com.enosisbd.api.server.entity.Role;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.model.ProviderType;
import com.enosisbd.api.server.security.mapper.MapToSecurityUser;
import com.enosisbd.api.server.service.authentication.AuthenticationService;
import com.enosisbd.api.server.service.oauth.GoogleTokenService;
import com.enosisbd.api.server.service.passwordReset.PasswordResetService;
import com.enosisbd.api.server.service.role.RoleService;
import com.enosisbd.api.server.service.token.JwtTokenService;
import com.enosisbd.api.server.service.user.UserService;
import com.nimbusds.jose.JOSEException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class AuthenticationServiceImpl implements AuthenticationService {

    private final UserService userService;
    private final AuthenticationManager authenticationManager;
    private final JwtTokenService jwtTokenService;
    private final PasswordEncoder passwordEncoder;
    private final RoleService roleService;
    private final PasswordResetService passwordResetService;
    private final GoogleTokenService googleTokenService;

    @Override
    public ResponseEntity<SignInResponseDTO> signIn(SignInDTO signInDTO) throws UserIsNotActiveException {
        Optional<User> userOpt = userService.findByEmail(signInDTO.getUsername());
        if (userOpt.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        if (userOpt.get().getApproved() == null || !userOpt.get().getApproved()) {
            throw new UserIsNotActiveException(
                    "Your account is currently inactive. It may be awaiting approval or has been disabled by the Administrator.");
        }

        User user = userOpt.get();
        MapToSecurityUser mapToSecurityUser = new MapToSecurityUser(user);
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(signInDTO.getUsername(), signInDTO.getPassword(), mapToSecurityUser.getAuthorities());
        Authentication authenticate = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authenticate);
        String accessToken = null;
        String refreshToken = null;
        try {
            List<String> authorities = mapToSecurityUser.getAuthorities()
                    .stream().map(GrantedAuthority::getAuthority).toList();
            accessToken = jwtTokenService.generateAccessToken(user.getEmail(), authorities, ProviderType.LOCAL.getName());

            refreshToken = jwtTokenService.generateRefreshToken(user.getEmail(), authorities, ProviderType.LOCAL.getName());
        } catch (JOSEException e) {
            throw new RuntimeException(e);
        }
        return ResponseEntity.ok().body(SignInResponseDTO.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .build());
    }

    @Override
    public ResponseEntity<SignUpResponseDTO> signUp(SignUpDTO signUpDTO) throws UsernameAlreadyExistsException {
        Optional<User> userOpt = userService.findByEmail(signUpDTO.getUsername());
        if (userOpt.isPresent()) {
            throw new UsernameAlreadyExistsException(String.format("%s user already exists in the system.", userOpt.get().getEmail()));
        }
        User user = new User();
        user.setFullName(signUpDTO.getFullName());
        user.setEmail(signUpDTO.getUsername());
        user.setPassword(passwordEncoder.encode(signUpDTO.getPassword()));
        user.setProvider(ProviderType.LOCAL.getName());
        Optional<Role> userRole = roleService.findByName("USER");
        user.setRoles(Set.of(userRole.get()));
        User persistedUser = userService.persist(user);
        return ResponseEntity.ok(SignUpResponseDTO.builder()
                .userId(persistedUser.getId().toString())
                .message("user is successfully registered.")
                .build());
    }

    @Override
    @Transactional
    public ResponseEntity<ForgotPasswordResponseDTO> forgotPassword(ForgotPasswordDTO forgotPasswordDTO) {
        Optional<User> user = userService.findByEmail(forgotPasswordDTO.getEmail());
        if (user.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(ForgotPasswordResponseDTO.builder()
                            .message("No user registered with that email")
                            .success(false)
                            .build());
        }
        passwordResetService.createPasswordResetTokenForUser(user.get());
        return ResponseEntity.ok(
                ForgotPasswordResponseDTO.builder()
                        .message("Password reset email sent successfully")
                        .success(true)
                        .build()
        );
    }

    @Override
    @Transactional
    public ResponseEntity<ResetPasswordResponseDTO> resetPassword(ResetPasswordDTO resetPasswordDTO) {
        String tokenValidation = passwordResetService.validatePasswordResetToken(resetPasswordDTO.getPasswordResetToken());
        if (!"valid".equals(tokenValidation)) {
            return ResponseEntity.badRequest()
                    .body(ResetPasswordResponseDTO.builder()
                            .message("Invalid or expired token")
                            .success(false)
                            .build());
        }
        User user = passwordResetService.getUserByPasswordResetToken(resetPasswordDTO.getPasswordResetToken());
        if (user == null) {
            return ResponseEntity.badRequest()
                    .body(ResetPasswordResponseDTO.builder()
                            .message("User not found")
                            .success(false)
                            .build());
        }

        passwordResetService.changeUserPassword(user, resetPasswordDTO.getNewPassword());
        passwordResetService.markTokenAsUsed(resetPasswordDTO.getPasswordResetToken());

        return ResponseEntity.ok(
                ResetPasswordResponseDTO.builder()
                        .message("Password updated successfully")
                        .success(true)
                        .build()
        );
    }

    @Override
    public ResponseEntity<RefreshTokenResponseDTO> refreshToken(RefreshTokenDTO refreshTokenDTO) throws UserIsNotActiveException, JOSEException {
        boolean valid = jwtTokenService.validateRefreshToken(refreshTokenDTO.getRefreshToken());
        if (!valid) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        String username = jwtTokenService.extractUsername(refreshTokenDTO.getRefreshToken());
        Optional<User> userOpt = userService.findByEmail(username);
        if (userOpt.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        User user = userOpt.get();
        if (!user.isActive()) {
            throw new UserIsNotActiveException(String.format("Manager Approved : %s", user.getApproved()));
        }

        MapToSecurityUser mapToSecurityUser = new MapToSecurityUser(user);
        List<String> authorities = mapToSecurityUser.getAuthorities()
                .stream().map(GrantedAuthority::getAuthority).toList();




        String authProvider = jwtTokenService.getAuthProvider(refreshTokenDTO.getRefreshToken());

        // Generate new access and refresh tokens with the same authProvider
        String newAccessToken = jwtTokenService.generateAccessToken(user.getEmail(), authorities, authProvider);
        String newRefreshToken = jwtTokenService.generateRefreshToken(user.getEmail(), authorities, authProvider);

        return ResponseEntity.status(HttpStatus.OK).body(RefreshTokenResponseDTO.builder()
                .accessToken(newAccessToken)
                .refreshToken(newRefreshToken)  // Include the refresh token
                .build());
    }

    @Override
    public ResponseEntity<SignInResponseDTO> processOAuth2Login(Map<String, Object> attributes, String provider,
            String googleAccessToken, String googleRefreshToken, Integer expiresIn) {
        String email = (String) attributes.get("email");
        String name = (String) attributes.get("name");
        String providerId = (String) attributes.get("sub");
        String pictureUrl = (String) attributes.get("picture");

        Optional<User> userOpt = userService.findByEmail(email);
        User user;

        if (userOpt.isEmpty()) {
            // Create new user
            user = new User();
            user.setEmail(email);
            user.setFullName(name);
            user.setProvider(provider);
            user.setProviderId(providerId);
            user.setPictureUrl(pictureUrl);
            user.setApproved(true);

            Optional<Role> userRole = roleService.findByName("USER");
            user.setRoles(Set.of(userRole.get()));
            user = userService.persist(user);
        } else {
            user = userOpt.get();
        }

        if (user.getProvider().equals(ProviderType.LOCAL.getName())) {
            return ResponseEntity.status(409).body(SignInResponseDTO.builder()
                    .build());
        }

        // Store Google OAuth tokens for accessing Google APIs
        googleTokenService.storeTokens(user, googleAccessToken, googleRefreshToken, expiresIn);

        // Generate JWT tokens for our application
        String accessToken = null;
        String refreshToken = null;
        try {
            MapToSecurityUser mapToSecurityUser = new MapToSecurityUser(user);
            List<String> authorities = mapToSecurityUser.getAuthorities()
                    .stream().map(GrantedAuthority::getAuthority).toList();

            accessToken = jwtTokenService.generateAccessToken(user.getEmail(), authorities, provider);
            refreshToken = jwtTokenService.generateRefreshToken(user.getEmail(), authorities, provider);
        } catch (JOSEException e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok().body(SignInResponseDTO.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .tokenType("Bearer")
            .build());
    }
}
