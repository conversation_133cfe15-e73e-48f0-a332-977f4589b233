package com.enosisbd.api.server.controller.reports;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


public interface ReportsApi {
    @GetMapping(value = "/reports/{projectId}/{versionId}")
    ResponseEntity<?> getReports(@PathVariable Long projectId, @PathVariable Long versionId);
}
