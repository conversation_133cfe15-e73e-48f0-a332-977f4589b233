package com.enosisbd.api.server.entity;

import java.time.LocalDateTime;
import java.util.Optional;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(indexes = {
    @Index(name = "idx_test_script_submodule", columnList = "submodule_id")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TestScript extends BaseEntity {

    @NotNull(message = "Code cannot be null")
    @Column(columnDefinition = "TEXT", nullable = false)
    private String code;

    @NotNull(message = "SubModule is required")
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "submodule_id", nullable = false)
    private SubModule subModule;

    public Long getSubModuleId() {
        return Optional.ofNullable(getSubModule())
                .map(SubModule::getId)
                .orElse(null);
    }
}
