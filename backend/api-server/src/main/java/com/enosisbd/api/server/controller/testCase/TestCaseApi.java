package com.enosisbd.api.server.controller.testCase;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.TestCaseDto;
import com.enosisbd.api.server.dto.TestCasesListDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface TestCaseApi {
    @PostMapping
    @Operation(summary = "Create a new test case")
    @ApiResponse(responseCode = "201", description = "Test case created successfully")
    ResponseEntity<RestResponse<TestCaseDto>> add(@Valid @RequestBody TestCaseDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get test case by ID")
    @RequiresEntityAccess(entityType = EntityType.TEST_CASE)
    RestResponse<TestCaseDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update test case")
    @RequiresEntityAccess(entityType = EntityType.TEST_CASE)
    RestResponse<TestCaseDto> update(
            @PathVariable Long id,
            @Valid @RequestBody TestCaseDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete test case")
    @ApiResponse(responseCode = "204", description = "Test case deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.TEST_CASE)
    ResponseEntity<Void> delete(@PathVariable Long id);

    @PostMapping("/batch")
            @Operation(summary = "Create or update multiple test cases")
    RestResponse<List<TestCaseDto>> createOrUpdateTestCases(
            @Valid @RequestBody TestCasesListDto testCases);
}
