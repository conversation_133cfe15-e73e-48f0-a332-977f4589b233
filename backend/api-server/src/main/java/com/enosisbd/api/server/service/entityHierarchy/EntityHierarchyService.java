package com.enosisbd.api.server.service.entityHierarchy;

import com.enosisbd.api.server.model.EntityType;

import java.util.List;
import java.util.Map;

public interface EntityHierarchyService {
    EntityType getParentEntityType(EntityType entityType);

    Map.Entry<EntityType, Long> getParentEntity(EntityType entityType, Long entityId);

    String getEntityCreator(EntityType entityType, Long entityId);

    List<Map.Entry<EntityType, Long>> getChildEntities(EntityType entityType, Long entityId);

    boolean entityExists(EntityType entityType, Long entityId);
}
