package com.enosisbd.api.server.controller.approval;

import com.enosisbd.api.server.dto.ApprovalDTO.*;
import com.enosisbd.api.server.service.approval.AdminApprovalService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RestController
@Tag(name = "Approval Operations")
@RequestMapping("/api/approvals")
@RequiredArgsConstructor
public class AdminApprovalController implements AdminApprovalApi {

    private final AdminApprovalService adminApprovalService;

    @PostMapping
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<AdminUserApproveResponseDTO> approveOrUnapproved(@Valid @RequestBody AdminUserApproveDTO request) {
        return adminApprovalService.approveOrUnapproved(request);
    }

}
