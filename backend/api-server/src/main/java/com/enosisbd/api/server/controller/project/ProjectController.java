package com.enosisbd.api.server.controller.project;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.dto.CrawledPageSummaryDto;
import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.ProjectProcessingResultDto;
import com.enosisbd.api.server.dto.TreeNodeDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.crawledPage.CrawledPageService;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.tree.ProjectTreeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/projects")
@RequiredArgsConstructor
@Tag(name = "Project Controller", description = "APIs for project management")
public class ProjectController implements ProjectApi {

    private final ProjectService projectService;
    private final ModuleService moduleService;
    private final CrawledPageService crawledPageService;
    private final ProjectTreeService projectTreeService;

    @GetMapping
    @Operation(summary = "Get all projects")
    @Override
    public RestResponse<List<ProjectDto>> getAll() {
        return RestResponse.of(projectService.findAll());
    }

    @PostMapping
    @Operation(summary = "Create a new project")
    @ApiResponse(responseCode = "201", description = "Project created successfully")
    @Override
    public ResponseEntity<RestResponse<ProjectDto>> add(@Valid @RequestBody ProjectDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(projectService.add(dto)));
    }

    @PostMapping("/with-validation")
    @Operation(summary = "Create a new project with validation and auto-correction")
    @ApiResponse(responseCode = "201", description = "Project created successfully with validation results")
    @Override
    public ResponseEntity<RestResponse<ProjectProcessingResultDto>> addWithValidation(@Valid @RequestBody ProjectDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(projectService.addWithValidation(dto)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get project by ID")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public RestResponse<ProjectDto> getById(@PathVariable Long id) {
        return projectService.getById(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Project not found with ID: " + id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update project")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public RestResponse<ProjectDto> update(
            @PathVariable Long id,
            @Valid @RequestBody ProjectDto dto) {
        dto.setId(id);
        return projectService.update(dto)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Project not found with ID: " + id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete project")
    @ApiResponse(responseCode = "204", description = "Project deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        return projectService.delete(id)
                .map(ignored -> ResponseEntity.noContent().<Void>build())
                .orElseThrow(() -> NotFoundRestException.with("Project not found with ID: " + id));
    }

    @GetMapping("/{id}/modules")
    @Operation(summary = "Get versions for a modules")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public RestResponse<List<ModuleDto>> findModules(@PathVariable Long id) {
        if (!projectService.existsById(id)) {
            throw NotFoundRestException.with("Project not found with ID: " + id);
        }
        return RestResponse.of(moduleService.findByProjectId(id));
    }

    @GetMapping("/{projectId}/crawled-pages")
    @Operation(summary = "Get crawled pages for a project")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public RestResponse<List<CrawledPageDto>> findCrawledPages(@PathVariable Long projectId) {
        return RestResponse.of(crawledPageService.findByProjectId(projectId));
    }

    @GetMapping("/{id}/crawled-pages/summary")
    @Operation(summary = "Get crawled pages summary for a project (without DOMJson for performance)")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public RestResponse<List<CrawledPageSummaryDto>> findCrawledPagesSummary(@PathVariable Long id) {
        return RestResponse.of(crawledPageService.findByProjectIdSummary(id));
    }

    @GetMapping("/{id}/tree")
    @Operation(summary = "Get project tree structure including modules and submodules")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public RestResponse<TreeNodeDto> getProjectTree(@PathVariable Long id) {
        // Delegate to the service layer
        TreeNodeDto treeNode = projectTreeService.getProjectTree(id);
        return RestResponse.of(treeNode);
    }

    @PostMapping("/{id}/refresh-google-sheet")
    @Operation(summary = "Refresh project data from Google Sheet")
    @RequiresEntityAccess(entityType = EntityType.PROJECT)
    @Override
    public RestResponse<ProjectDto> refreshFromGoogleSheet(@PathVariable Long id) {
        return projectService.refreshFromGoogleSheet(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Project not found with ID: " + id));
    }
}
