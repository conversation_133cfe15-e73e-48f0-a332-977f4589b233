package com.enosisbd.api.server.controller.googlesheets;

import com.enosisbd.api.server.dto.GoogleSheetImportRequestDto;
import com.enosisbd.api.server.dto.GoogleSheetImportResponseDto;
import com.enosisbd.api.server.model.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.security.GeneralSecurityException;

public interface GoogleSheetsApi {
    
    @PostMapping("/import")
    @Operation(summary = "Import data from a Google Sheet")
    @ApiResponse(responseCode = "201", description = "Project created successfully from Google Sheet")
    ResponseEntity<RestResponse<GoogleSheetImportResponseDto>> importFromGoogleSheet(
            @Valid @RequestBody GoogleSheetImportRequestDto request) 
            throws IOException, GeneralSecurityException;
}
