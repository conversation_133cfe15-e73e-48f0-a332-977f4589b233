package com.enosisbd.api.server.security;

import com.enosisbd.api.server.security.mapper.MapToSecurityUser;
import com.enosisbd.api.server.service.user.UserService;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import jakarta.annotation.PostConstruct;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandlerImpl;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Log4j2
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true)
public class SecurityConfig {

    private static final String[] PUBLIC_URLS = {
            "/api/auth/*",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/.well-known/jwks",
            "/api/auth/oauth2/**"
    };

    @Value("${app.security.key-rotation.enabled:true}")
    private boolean keyRotationEnabled;

    @Value("${app.security.key-rotation.interval-hours:24}")
    private int rotationIntervalHours;

    @Value("${app.security.key-rotation.key-keep-count:3}")
    private int keyKeepCount;

    @Value("${app.backend.url}")
    private String backendUrl;

    private final UserService userService;

    private final List<RSAKey> rsaKeys = new LinkedList<>();
    private volatile RSAKey currentRsaKey;

    @PostConstruct
    public void init() {
        rotateKey();
        if (keyRotationEnabled) {
            log.info("Key rotation enabled with interval: {} hours", rotationIntervalHours);
        }
    }

    @Bean
    public RSAKey rsaKey() {
        return currentRsaKey;
    }

    public JWKSet jwkSet() {
        List<JWK> jwks = rsaKeys.stream()
                .map(RSAKey::toPublicJWK)
                .collect(Collectors.toList());
        return new JWKSet(jwks);
    }

    @Scheduled(fixedRateString = "${app.security.key-rotation.interval-hours}", timeUnit = TimeUnit.HOURS)
    public void rotateKey() {
        if (!keyRotationEnabled) return;

        synchronized (this) {
            log.info("Rotating RSA key...");
            // Generate new key
            RSAKey newKey = generateNewRsaKey();
            rsaKeys.add(0, newKey);
            currentRsaKey = newKey;

            // Trim old keys
            while (rsaKeys.size() > keyKeepCount) {
                rsaKeys.remove(rsaKeys.size() - 1);
            }

            log.info("Key rotation completed. Current key ID: {}", newKey.getKeyID());
            log.debug("Active key IDs: {}",
                    rsaKeys.stream().map(RSAKey::getKeyID).toList());
        }
    }

    private RSAKey generateNewRsaKey() {
        KeyPairGenerator keyPairGenerator;
        try {
            keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();

            return new RSAKey.Builder((RSAPublicKey) keyPair.getPublic())
                    .privateKey((RSAPrivateKey) keyPair.getPrivate())
                    .keyID("api-server-kid-" + Instant.now().getEpochSecond())
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to generate RSA key pair", e);
        }
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(AbstractHttpConfigurer::disable)
                .cors(Customizer.withDefaults())
                .formLogin(AbstractHttpConfigurer::disable)
                .httpBasic(AbstractHttpConfigurer::disable)
                .logout(AbstractHttpConfigurer::disable)
                .sessionManagement(
                        httpSecuritySessionManagementConfigurer ->
                                httpSecuritySessionManagementConfigurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                .authorizeHttpRequests(
                        authorizeReq -> authorizeReq.requestMatchers(PUBLIC_URLS)
                                .permitAll()
                                .anyRequest().authenticated()
                )
                .oauth2ResourceServer(
                        httpSecurityOAuth2ResourceServerConfigurer ->
                                httpSecurityOAuth2ResourceServerConfigurer.jwt(
                                        jwtConfigurer -> jwtConfigurer.decoder(jwtDecoder())
                                                .jwtAuthenticationConverter(jwtAuthenticationConverter())
                                )
                )
                .exceptionHandling(
                        httpSecurityExceptionHandlingConfigurer ->
                                httpSecurityExceptionHandlingConfigurer.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint())
                                        .accessDeniedHandler(new AccessDeniedHandlerImpl())
                                        .configure(http)
                );
        return http.build();
    }

    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtGrantedAuthoritiesConverter converter = new JwtGrantedAuthoritiesConverter();
        converter.setAuthorityPrefix("");
        converter.setAuthoritiesClaimName("roles");
        JwtAuthenticationConverter jwtConverter = new JwtAuthenticationConverter();
        jwtConverter.setJwtGrantedAuthoritiesConverter(converter);
        return jwtConverter;
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(backendUrl + "/.well-known/jwks").build();
    }

    @Bean
    @Transactional
    public UserDetailsService userDetailsService() {
        return username -> userService.findByEmail(username)
                .map(MapToSecurityUser::new)
                .orElseThrow(() -> new UsernameNotFoundException(username));
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }


}
