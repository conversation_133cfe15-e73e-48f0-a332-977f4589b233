package com.enosisbd.api.server.service.authorization.impl;

import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.repository.UserRepository;
import com.enosisbd.api.server.security.utils.SecurityContextUtils;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;

/**
 * Utility class for authorization checks
 */
@Service
@RequiredArgsConstructor
public class AuthorizationServiceImpl implements AuthorizationService {

    private static final String ADMIN_ROLE = "ROLE_ADMIN";
    private static final String ADMIN = "ADMIN";
    private final UserRepository userRepository;

    /**
     * Checks if a user is an admin
     * @param userId The user ID to check
     * @return true if the user has admin role, false otherwise
     */
    @Override
    public boolean isAdmin(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BadRequestRestException("User not found with ID: " + userId));
        
        if (user == null) {
            return false;
        }
        
        return user.getRoles().stream()
                .anyMatch(role -> role.getName().equals(ADMIN));
    }

    /**
     * Gets the current user ID
     * @return The current user ID
     */
    @Override
    public Long getCurrentUserId() {
        Authentication authentication = SecurityContextUtils.getCurrentUser();
        String email = authentication.getName();
        
        return userRepository.findByEmailIgnoreCase(email)
                .map(User::getId)
                .orElse(null);
    }
    
    /**
     * Checks if the current user has admin role
     * @return true if the user has admin role, false otherwise
     */
    @Override
    public boolean isCurrentUserAdmin() {
        Authentication authentication = SecurityContextUtils.getCurrentUser();
        return authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> authority.equals(ADMIN_ROLE));
    }
    
    /**
     * Gets the current username
     * @return The current username
     */
    @Override
    public String getCurrentUsername() {
        Authentication authentication = SecurityContextUtils.getCurrentUser();
        return authentication.getName();
    }
}
