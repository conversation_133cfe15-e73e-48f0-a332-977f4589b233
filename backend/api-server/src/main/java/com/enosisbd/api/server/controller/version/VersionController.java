package com.enosisbd.api.server.controller.version;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.TestSuiteDto;
import com.enosisbd.api.server.dto.VersionDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.testSuite.TestSuiteService;
import com.enosisbd.api.server.service.version.VersionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/versions")
@RequiredArgsConstructor
@Tag(name = "Version Controller", description = "APIs for version management")
public class VersionController implements VersionApi {
    private final VersionService service;
    private final TestSuiteService testSuiteService;

    @PostMapping
    @Operation(summary = "Create a new version")
    @ApiResponse(responseCode = "201", description = "Version created successfully")
    @Override
    public ResponseEntity<RestResponse<VersionDto>> add(@Valid @RequestBody VersionDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(service.add(dto)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get version by ID")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    @Override
    public RestResponse<VersionDto> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Version not found with ID: " + id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update version")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    @Override
    public RestResponse<VersionDto> update(
            @PathVariable Long id,
            @Valid @RequestBody VersionDto dto) {
        dto.setId(id);
        return service.update(dto)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Version not found with ID: " + id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete version")
    @ApiResponse(responseCode = "204", description = "Version deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        return service.delete(id)
                .map(ignored -> ResponseEntity.noContent().<Void>build())
                .orElseThrow(() -> NotFoundRestException.with("Version not found with ID: " + id));
    }

    @GetMapping("/{id}/test-suites")
    @Operation(summary = "Get test suites for a version")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    @Override
    public RestResponse<List<TestSuiteDto>> findTestSuites(@PathVariable Long id) {
        if (!service.existsById(id)) {
            throw NotFoundRestException.with("Version not found with ID: " + id);
        }
        return RestResponse.of(testSuiteService.findByVersionId(id));
    }
}
