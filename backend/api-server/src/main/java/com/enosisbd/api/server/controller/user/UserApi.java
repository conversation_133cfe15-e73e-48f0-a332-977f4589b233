package com.enosisbd.api.server.controller.user;

import com.enosisbd.api.server.dto.UserDTO;
import com.enosisbd.api.server.dto.UserProfileDTO;
import com.enosisbd.api.server.dto.UserPreferenceDTO;
import com.enosisbd.api.server.dto.PasswordChangeDTO;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface UserApi {
    @GetMapping("/list")
    ResponseEntity<List<UserDTO>> list();

    @GetMapping("/profile")
    UserProfileDTO getCurrentUserProfile();

    @PutMapping("/profile")
    UserProfileDTO updateUserProfile(@Valid @RequestBody UserProfileDTO profileDTO);

    @PostMapping("/change-password")
    ResponseEntity<?> changePassword(@Valid @RequestBody PasswordChangeDTO passwordChangeDTO);

    @GetMapping("/preferences")
    UserPreferenceDTO getUserPreferences();

    @PutMapping("/preferences")
    UserPreferenceDTO updateUserPreferences(@Valid @RequestBody UserPreferenceDTO preferenceDTO);
}
