package com.enosisbd.api.server.controller.reports;

import com.enosisbd.api.server.dto.ReportsDTOs;
import com.enosisbd.api.server.service.reports.ReportsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * The type Reports controller.
 */
@Log4j2
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ReportsController implements ReportsApi {
    private final ReportsService reportsService;

    @GetMapping(value = "/reports/{projectId}/{versionId}")
    @Override
    public ResponseEntity<?> getReports(@PathVariable Long projectId, @PathVariable Long versionId) {
        return ResponseEntity.ok(reportsService.findReports(projectId, versionId));
    }
}
