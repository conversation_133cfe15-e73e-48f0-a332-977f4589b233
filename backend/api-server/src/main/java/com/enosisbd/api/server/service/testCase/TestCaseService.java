package com.enosisbd.api.server.service.testCase;

import com.enosisbd.api.server.dto.TestCaseDto;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface TestCaseService {
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    Optional<TestCaseDto> getById(Long id);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    TestCaseDto add(TestCaseDto dto);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    Optional<TestCaseDto> update(TestCaseDto dto);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    Optional<Boolean> delete(Long id);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    List<TestCaseDto> findByTestSuiteId(Long testSuiteId);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    List<TestCaseDto> saveOrUpdateList(Long testSuiteId, List<TestCaseDto> testCases);
}
