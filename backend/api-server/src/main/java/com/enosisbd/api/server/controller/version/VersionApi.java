package com.enosisbd.api.server.controller.version;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.TestSuiteDto;
import com.enosisbd.api.server.dto.VersionDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface VersionApi {
    @PostMapping
    @Operation(summary = "Create a new version")
    @ApiResponse(responseCode = "201", description = "Version created successfully")
    ResponseEntity<RestResponse<VersionDto>> add(@Valid @RequestBody VersionDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get version by ID")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    RestResponse<VersionDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update version")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    RestResponse<VersionDto> update(
            @PathVariable Long id,
            @Valid @RequestBody VersionDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete version")
    @ApiResponse(responseCode = "204", description = "Version deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    ResponseEntity<Void> delete(@PathVariable Long id);

    @GetMapping("/{id}/test-suites")
    @Operation(summary = "Get test suites for a version")
    @RequiresEntityAccess(entityType = EntityType.VERSION)
    RestResponse<List<TestSuiteDto>> findTestSuites(@PathVariable Long id);
}
