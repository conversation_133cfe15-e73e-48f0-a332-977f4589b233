package com.enosisbd.api.server.annotation;

import com.enosisbd.api.server.model.EntityType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to indicate that a method requires entity access check.
 * If entityType is DYNAMIC, the entity type will be extracted from the method parameters.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiresEntityAccess {
    /**
     * Special constant to indicate that the entity type should be extracted dynamically
     * from the method parameters.
     */
    String DYNAMIC = "DYNAMIC";

    /**
     * The type of entity to check access for.
     * If set to DYNAMIC, the entity type will be extracted from the method parameters.
     */
    EntityType entityType() default EntityType.PROJECT;

    /**
     * Flag to indicate if the entity type should be extracted dynamically.
     * If true, the entity type will be extracted from the method parameters.
     */
    boolean isDynamic() default false;

    /**
     * The name of the parameter that contains the entity ID.
     * Default is "id".
     */
    String idParam() default "id";
}
