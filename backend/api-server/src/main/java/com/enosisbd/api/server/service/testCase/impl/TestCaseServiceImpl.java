package com.enosisbd.api.server.service.testCase.impl;

import com.enosisbd.api.server.dto.TestCaseDto;
import com.enosisbd.api.server.entity.TestCase;
import com.enosisbd.api.server.entity.TestSuite;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.TestCaseRepository;
import com.enosisbd.api.server.repository.TestSuiteRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.testCase.TestCaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class TestCaseServiceImpl implements TestCaseService {
    private final TestCaseRepository repository;
    private final TestSuiteRepository testSuiteRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestCaseDto> getById(Long id) {
        // Access check is handled by the @RequiresEntityAccess annotation
        return repository.findByIdJoined(id)
                .map(this::convertToDto);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public TestCaseDto add(TestCaseDto dto) {
        TestCase entity = convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long testSuiteId = dto.getTestSuiteId();
        if (testSuiteId != null) {
            // Check if user has access to the test suite
            TestSuite testSuite = testSuiteRepository
                    .findById(testSuiteId)
                    .orElseThrow(() -> BadRequestRestException.with("Test suite not found with ID: " + testSuiteId));

            if (!entitySharingService.hasAccess(testSuite, EntityType.TEST_SUITE)) {
                throw new BadRequestRestException("You don't have access to this test suite");
            }

            entity.setTestSuite(testSuite);
        }

        repository.save(entity);
        return convertToDto(entity);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestCaseDto> update(TestCaseDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        TestCase entity = convertToEntity(maybe.get(), dto);

        // Access check is handled by the @RequiresEntityAccess annotation

        boolean isValidId = dto.getTestSuiteId() != null
                && !Objects.equals(dto.getTestSuiteId(), maybe.get().getTestSuiteId());

        if (isValidId) {
            // Check if user has access to the test suite
            TestSuite testSuite = testSuiteRepository
                    .findById(dto.getTestSuiteId())
                    .orElseThrow(() -> BadRequestRestException.with("Test suite not found with ID: " + dto.getTestSuiteId()));
            entity.setTestSuite(testSuite);
        }

        repository.save(entity);
        return Optional.of(convertToDto(entity));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<TestCase> testCase = repository.findById(id);
        if (testCase.isEmpty()) {
            return Optional.empty();
        }

        // Access check is handled by the @RequiresEntityAccess annotation

        // Delete the test case
        repository.delete(testCase.get());
        return Optional.of(true);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<TestCaseDto> findByTestSuiteId(Long testSuiteId) {
        // Get all test cases for the test suite
        List<TestCase> allTestCases = repository.findByTestSuiteIdJoined(testSuiteId);
        if (allTestCases.isEmpty()) {
            return List.of();
        }

        // Convert to DTOs
        return allTestCases.stream()
                .map(this::convertToDto)
                .sorted((a, b) -> a.getTestCaseId().compareTo(b.getTestCaseId()))
                .toList();
    }

    /**
     * Create Or Update multiple test cases for a specific test suite in a single operation
     * @param testSuiteId The ID of the test suite to add test cases to
     * @param testCases The list of test cases to add
     * @return The list of saved test cases with their IDs
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<TestCaseDto> saveOrUpdateList(Long testSuiteId, List<TestCaseDto> testCases) {
        // Check if user has access to the test suite
        TestSuite testSuite = testSuiteRepository.findById(testSuiteId)
                .orElseThrow(() -> BadRequestRestException.with("Test suite not found with ID: " + testSuiteId));

        if (!entitySharingService.hasAccess(testSuite, EntityType.TEST_SUITE)) {
            throw new BadRequestRestException("You don't have access to this test suite");
        }

        List<TestCase> existingTestCases = repository.findByTestSuiteIdJoined(testSuiteId);

        List<Long> incomingIds = testCases.stream()
                .filter(tc -> tc.getId() != null && tc.getId() != -1)
                .map(TestCaseDto::getId)
                .toList();

        List<TestCase> testCasesToDelete = existingTestCases.stream()
                .filter(tc -> !incomingIds.contains(tc.getId()))
                .toList();

        testCasesToDelete.forEach(tc -> repository.deleteById(tc.getId()));

        List<TestCase> entities = new ArrayList<>();

        for (TestCaseDto dto : testCases) {
            TestCase entity;
            if (dto.getId() == null || dto.getId() == -1) {
                dto.setId(null);
                entity = convertToEntity(dto);
                // Set creator for new entities
                entity.setCreatedBy(authorizationService.getCurrentUsername());
            } else {
                TestCase existingEntity = existingTestCases.stream()
                    .filter(tc -> tc.getId().equals(dto.getId()))
                    .findFirst()
                    .orElseThrow(() -> new BadRequestRestException("Test case not found: " + dto.getId()));
                entity = convertToEntity(existingEntity, dto);
            }
            entity.setTestSuite(testSuite);
            entities.add(entity);
        }

        repository.saveAll(entities);

        return findByTestSuiteId(testSuiteId);
    }

    private TestCaseDto convertToDto(TestCase entity) {
        return convertToDto(new TestCaseDto(), entity);
    }

    private TestCaseDto convertToDto(TestCaseDto dto, TestCase entity) {
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setTestCaseId(entity.getTestCaseId());
        dto.setDescription(entity.getDescription());
        dto.setSteps(entity.getSteps());
        dto.setExpectedResult(entity.getExpectedResult());
        dto.setTestSuiteId(entity.getTestSuiteId());
        dto.setStatus(entity.getStatus());
        return dto;
    }

    private TestCase convertToEntity(TestCaseDto dto) {
        return convertToEntity(new TestCase(), dto);
    }

    private TestCase convertToEntity(TestCase entity, TestCaseDto dto) {
        entity.setId(dto.getId());
        entity.setTestCaseId(dto.getTestCaseId());
        entity.setDescription(dto.getDescription());
        entity.setSteps(dto.getSteps());
        entity.setExpectedResult(dto.getExpectedResult());
        entity.setStatus(dto.getStatus());
        return entity;
    }
}
