package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.SharableEntityType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SharableEntityTypeRepository extends JpaRepository<SharableEntityType, Long> {
    
    Optional<SharableEntityType> findByName(String name);
    
    List<SharableEntityType> findByActiveTrue();
}
