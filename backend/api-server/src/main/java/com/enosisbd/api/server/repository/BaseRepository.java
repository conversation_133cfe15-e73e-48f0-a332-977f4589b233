package com.enosisbd.api.server.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * Base repository interface with common methods for all repositories
 * @param <T> The entity type
 * @param <ID> The ID type
 */
@NoRepositoryBean
public interface BaseRepository<T, ID> extends JpaRepository<T, ID> {
    // Common repository methods can be added here
}
