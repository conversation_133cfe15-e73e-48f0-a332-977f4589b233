<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="20240602-move-google-sheets-metadata-to-project" author="enosis">
        <comment>Move Google Sheets metadata from Module to Project table and add user email field</comment>
        
        <!-- Add Google Sheets metadata columns to Project table -->
        <addColumn tableName="project">
            <column name="google_sheet_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="google_sheet_url" type="varchar(1024)">
                <constraints nullable="true"/>
            </column>
            <column name="submodule_column" type="varchar(10)">
                <constraints nullable="true"/>
            </column>
            <column name="submodule_start_row" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="google_user_email" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        
        <!-- Create index on google_user_email for faster lookups -->
        <createIndex tableName="project" indexName="idx_project_google_user_email">
            <column name="google_user_email"/>
        </createIndex>
        
        <!-- Drop Google Sheets metadata columns from Module table -->
        <dropColumn tableName="module" columnName="google_sheet_id"/>
        <dropColumn tableName="module" columnName="google_sheet_name"/>
        <dropColumn tableName="module" columnName="submodule_column"/>
        <dropColumn tableName="module" columnName="submodule_start_row"/>
    </changeSet>
</databaseChangeLog>
