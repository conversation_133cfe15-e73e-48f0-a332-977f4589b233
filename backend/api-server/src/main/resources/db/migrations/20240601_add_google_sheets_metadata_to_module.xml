<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="20240601-add-google-sheets-metadata-to-module" author="enosis">
        <comment>Add Google Sheets metadata columns to Module table</comment>
        
        <addColumn tableName="module">
            <column name="google_sheet_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="google_sheet_name" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="submodule_column" type="varchar(10)">
                <constraints nullable="true"/>
            </column>
            <column name="submodule_start_row" type="int">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
