<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20240604-1" author="developer">
        <comment>Add test case related columns to project table</comment>
        <addColumn tableName="project">
            <column name="case_id_column" type="VARCHAR(1)">
                <constraints nullable="true"/>
            </column>
            <column name="test_case_description_column" type="VARCHAR(1)">
                <constraints nullable="true"/>
            </column>
            <column name="test_case_expected_result_column" type="VARCHAR(1)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
