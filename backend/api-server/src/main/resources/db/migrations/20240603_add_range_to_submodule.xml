<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="20240603-add-range-to-submodule" author="enosis">
        <comment>Add row range fields to SubModule table</comment>
        
        <addColumn tableName="sub_module">
            <column name="row_range" type="varchar(50)">
                <constraints nullable="true"/>
            </column>
            <column name="start_row" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="end_row" type="int">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
