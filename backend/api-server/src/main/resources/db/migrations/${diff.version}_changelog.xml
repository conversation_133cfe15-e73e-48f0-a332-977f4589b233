<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="mohibur (generated)" id="1748891899111-1">
        <createTable tableName="crawled_page">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="crawled_pagePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="crawl_option" type="JSONB"/>
            <column name="dom_json" type="JSONB"/>
            <column name="page_name" type="VARCHAR(255)"/>
            <column name="page_url" type="VARCHAR(255)"/>
            <column name="project_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-2">
        <createTable tableName="entity_access">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entity_accessPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="is_inherited" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="entity_type_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="shared_by_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="shared_with_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-3">
        <createTable tableName="module">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="modulePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="project_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-4">
        <createTable tableName="password_reset_tokens">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="password_reset_tokensPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="expiry_date" type="TIMESTAMP(6) WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="token" type="VARCHAR(255)"/>
            <column name="used" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-5">
        <createTable tableName="project">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="projectPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="case_id_column" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
            <column name="excluded_tabs" type="VARCHAR(255)"/>
            <column name="google_sheet_id" type="VARCHAR(255)"/>
            <column name="google_sheet_url" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="submodule_column" type="VARCHAR(255)"/>
            <column name="submodule_start_row" type="INTEGER"/>
            <column name="test_case_description_column" type="VARCHAR(255)"/>
            <column name="test_case_expected_result_column" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-6">
        <createTable tableName="roles">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="rolesPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-7">
        <createTable tableName="sharable_entity_types">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="sharable_entity_typesPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-8">
        <createTable tableName="sub_module">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="sub_modulePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="end_row" type="INTEGER"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="start_row" type="INTEGER"/>
            <column name="module_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-9">
        <createTable tableName="test_script">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="test_scriptPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="code" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="submodule_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-10">
        <createTable tableName="user_preferences">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_preferencesPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="entity_view_mode" type="VARCHAR(10)"/>
            <column name="user_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-11">
        <createTable tableName="users">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="usersPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="approved" type="BOOLEAN"/>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="full_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="google_access_token" type="VARCHAR(2048)"/>
            <column name="google_refresh_token" type="VARCHAR(512)"/>
            <column name="google_token_expiry" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="password" type="VARCHAR(100)"/>
            <column name="picture_url" type="VARCHAR(512)"/>
            <column name="provider" type="VARCHAR(10)"/>
            <column name="provider_id" type="VARCHAR(25)"/>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-12">
        <createTable tableName="users_roles">
            <column name="user_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="users_rolesPK"/>
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="users_rolesPK"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-13">
        <addUniqueConstraint columnNames="user_id" constraintName="UC_PASSWORD_RESET_TOKENSUSER_ID_COL" tableName="password_reset_tokens"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-14">
        <addUniqueConstraint columnNames="name" constraintName="UC_SHARABLE_ENTITY_TYPESNAME_COL" tableName="sharable_entity_types"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-15">
        <addUniqueConstraint columnNames="submodule_id" constraintName="UC_TEST_SCRIPTSUBMODULE_ID_COL" tableName="test_script"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-16">
        <addUniqueConstraint columnNames="email" constraintName="UC_USERSEMAIL_COL" tableName="users"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-17">
        <addUniqueConstraint columnNames="user_id" constraintName="UC_USER_PREFERENCESUSER_ID_COL" tableName="user_preferences"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-18">
        <addUniqueConstraint columnNames="shared_with_user_id, entity_type_id, entity_id" constraintName="UKh9qxdc8xagiuvinoqmb25fioe" tableName="entity_access"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-19">
        <createIndex indexName="idx_crawled_page_project" tableName="crawled_page">
            <column name="project_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-20">
        <createIndex indexName="idx_entity_access_entity" tableName="entity_access">
            <column name="entity_type_id"/>
            <column name="entity_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-21">
        <createIndex indexName="idx_entity_access_is_inherited" tableName="entity_access">
            <column name="shared_with_user_id"/>
            <column name="is_inherited"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-22">
        <createIndex indexName="idx_module_project" tableName="module">
            <column name="project_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-23">
        <createIndex indexName="idx_password_reset_token" tableName="password_reset_tokens">
            <column name="token"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-24">
        <createIndex indexName="idx_project_updated_at" tableName="project">
            <column name="updated_at"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-25">
        <createIndex indexName="idx_roles_name" tableName="roles">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-26">
        <createIndex indexName="idx_sharable_entity_type_name_active" tableName="sharable_entity_types">
            <column name="name"/>
            <column name="is_active"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-27">
        <createIndex indexName="idx_submodule_module" tableName="sub_module">
            <column name="module_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-28">
        <createIndex indexName="idx_users_approved" tableName="users">
            <column name="approved"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-29">
        <createIndex indexName="idx_users_full_name" tableName="users">
            <column name="full_name"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-30">
        <addForeignKeyConstraint baseColumnNames="submodule_id" baseTableName="test_script" constraintName="FK2nt7tjesiti46i8apk73xpuit" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="sub_module" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-31">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="users_roles" constraintName="FK2o0jvgh89lemvvo17cbqvdxaa" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-32">
        <addForeignKeyConstraint baseColumnNames="shared_with_user_id" baseTableName="entity_access" constraintName="FK779cc7q41iveynrat7dtodkcm" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-33">
        <addForeignKeyConstraint baseColumnNames="project_id" baseTableName="module" constraintName="FKcjhgsdebgmotrcyx8ws2mv3ot" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="project" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-34">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="user_preferences" constraintName="FKepakpib0qnm82vmaiismkqf88" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-35">
        <addForeignKeyConstraint baseColumnNames="shared_by_user_id" baseTableName="entity_access" constraintName="FKguwfqpfba6ygse0dirapvtykk" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-36">
        <addForeignKeyConstraint baseColumnNames="role_id" baseTableName="users_roles" constraintName="FKj6m8fwv7oqv74fcehir1a9ffy" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="roles" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-37">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="password_reset_tokens" constraintName="FKk3ndxg5xp6v7wd4gjyusp15gq" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-38">
        <addForeignKeyConstraint baseColumnNames="project_id" baseTableName="crawled_page" constraintName="FKs0bruihnpyu618d0v01vkfw0m" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="project" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-39">
        <addForeignKeyConstraint baseColumnNames="entity_type_id" baseTableName="entity_access" constraintName="FKscw42yr9hbhchb9b6vskgkh9o" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="sharable_entity_types" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1748891899111-40">
        <addForeignKeyConstraint baseColumnNames="module_id" baseTableName="sub_module" constraintName="FKsiy8j1vihx4qrh9ap5mdfkn13" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="module" validate="true"/>
    </changeSet>
</databaseChangeLog>
