<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="003-remove-google-user-email" author="system">
        <comment>Remove redundant google_user_email field from projects table since user context is available from authentication</comment>
        
        <dropColumn tableName="projects" columnName="google_user_email"/>
        
        <rollback>
            <addColumn tableName="projects">
                <column name="google_user_email" type="VARCHAR(255)">
                    <constraints nullable="true"/>
                </column>
            </addColumn>
        </rollback>
    </changeSet>

</databaseChangeLog>
