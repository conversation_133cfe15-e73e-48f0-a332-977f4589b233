# Security Setup Guide for OAuth Token Encryption

This guide explains how to set up secure encryption for OAuth tokens in the Enosis Portal application when setup for different environments for the first time.

## Overview

The application now encrypts OAuth tokens using AES-256-GCM encryption before storing them in the database. This follows industry security standards and protects sensitive user credentials.

## Encryption Key Generation

### Step 1: Generate Encryption Key

Run this Java code to generate a secure encryption key:

```java
import com.enosisbd.api.server.service.security.TokenEncryptionService;

public class KeyGenerator {
    public static void main(String[] args) {
        String encryptionKey = TokenEncryptionService.generateEncryptionKey();
        System.out.println("Generated encryption key: " + encryptionKey);
    }
}
```

### Step 2: Set Environment Variable

Set the generated key as an environment variable:

```text
# Development
In the application-dev.yaml
app.security.token.encryption-key=your-generated-key

# Production (Docker)
In the application-prod.yaml
app.security.token.encryption-key=your-generated-key

# Docker
docker run -e app.security.token.encryption.key=your-generated-key
```